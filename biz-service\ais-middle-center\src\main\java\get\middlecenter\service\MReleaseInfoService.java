package get.middlecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.aismiddle.dto.ReleaseInfoAndItemDto;
import com.get.aismiddle.dto.ReleaseInfoSearchDto;
import com.get.aismiddle.entity.MReleaseInfo;
import com.get.aismiddle.vo.ReleaseInfoAndItemVo;
import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import java.util.List;
import javax.validation.Valid;


/**
 * 发版信息
 */
public interface MReleaseInfoService extends IService<MReleaseInfo> {

    List<BaseSelectEntity> getPlatformTypeDropDown();

    List<ReleaseInfoAndItemVo> getReleaseInfoAndItem(@Valid ReleaseInfoSearchDto releaseInfoSearchDto, Page page);

    void addReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto);

    void deleteReleaseInfoAndItem(Long id);

    ReleaseInfoAndItemVo getDetailedInformationById(Long id);

    void editReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto);
}


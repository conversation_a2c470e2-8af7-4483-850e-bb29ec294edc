package com.get.partnercenter.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.get.core.log.exception.GetServiceException;
import com.get.core.redis.cache.GetRedis;
import com.get.partnercenter.config.WxConfig;
import com.get.partnercenter.constant.WxConstant;
import com.get.partnercenter.dto.wechat.LinkAndQrCodeVo;
import com.get.partnercenter.dto.wechat.MinProgramQrCodeDto;
import com.get.partnercenter.service.WeChatService;
import com.get.partnercenter.util.WxUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/7/21
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class WeChatServiceImpl implements WeChatService {

    @Resource
    private GetRedis getRedis;
    @Autowired
    private WxConfig wxConfig;

    @Override
    public String getAccessToken(String appId, String secret) {
        if (StringUtils.isBlank(appId)) {
            appId = wxConfig.getAppId();
            secret = wxConfig.getAppSecret();
        }
        log.info("appid:{}", appId);
        Object object = getRedis.get(WxConstant.WX_ACCESS_TOKEN_KEY + appId);
        if (Objects.nonNull(object)) {
            return object.toString();
        }
        try {
            String accessToken = WxUtils.getAccessToken(appId, secret);
            getRedis.setEx(WxConstant.WX_ACCESS_TOKEN_KEY + appId, accessToken, WxConstant.WX_ACCESS_TOKEN_EXPIRE_TIME);
            return accessToken;
        } catch (Exception e) {
            log.error("获取微信access_token失败:{}", e.getMessage());
            throw new GetServiceException("获取微信access_token失败");
        }
    }

    @Override
    public Object getQrCode(String appId, MinProgramQrCodeDto minProgramQrCodeDto) {
        String accessToken = getAccessToken(wxConfig.getAppId(), wxConfig.getAppSecret());
        String url = WxConstant.WX_CREATE_QR_CODE_URL + accessToken;
        HttpResponse response = HttpRequest.post(url)
                .body(JSON.toJSONString(minProgramQrCodeDto), "application/json")
                .execute();
        // 获取响应体和Content-Type
        String contentType = response.header("Content-Type");
        byte[] bodyBytes = response.bodyBytes();

        // 判断是否是 JSON（说明失败）
        if (contentType != null && contentType.contains("application/json")) {
            String jsonStr = new String(bodyBytes, StandardCharsets.UTF_8);
            JSONObject resultJson = JSONObject.parseObject(jsonStr);
            log.error("获取小程序二维码失败: {}", resultJson);
            throw new GetServiceException("获取小程序二维码失败：" + resultJson.getString("errmsg"));
        }

        // 否则是图片 Buffer（成功）
        log.info("获取二维码成功");
        // 转为 Base64 返回给前端
        String base64 = Base64.getEncoder().encodeToString(bodyBytes);
        return "data:image/jpeg;base64," + base64;

    }

    @Override
    public Object getUrlLink(String appId, MinProgramQrCodeDto minProgramQrCodeDto) {
        String accessToken = getAccessToken(wxConfig.getAppId(), wxConfig.getAppSecret());
        String url = WxConstant.WX_GENERATE_URL_LINK_URL + accessToken;
        JSONObject jsonObject= new JSONObject();
        jsonObject.put("path", "pages/index/index");
        HttpResponse response = HttpRequest.post(url)
                .body(JSON.toJSONString(jsonObject), "application/json")
                .execute();
        String body = response.body();
        JSONObject object = JSONObject.parseObject(body);
        if (object.containsKey("errcode") && object.getInteger("errcode") != 0) {
            log.error("获取小程序码链接失败: {}", object);
            throw new GetServiceException("获取小程序码链接失败：" + object.getString("errmsg"));
        }
        log.info("获取小程序码链接成功: {}",object);
        return object.getString("url_link");
    }

    @Override
    public LinkAndQrCodeVo getLinkAndQrCode(String appId, MinProgramQrCodeDto minProgramQrCodeDto) {
        Object urlLink = getUrlLink(appId, minProgramQrCodeDto);
        Object qrCode = getQrCode(appId, minProgramQrCodeDto);
        return LinkAndQrCodeVo.builder()
                .url(urlLink.toString())
                .qrCode(qrCode.toString())
                .build();
    }

    /**
     * 判断字符串是否为json
     *
     * @param str
     * @return
     */
    private boolean isJson(String str) {
        try {
            JSONObject.parseObject(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}

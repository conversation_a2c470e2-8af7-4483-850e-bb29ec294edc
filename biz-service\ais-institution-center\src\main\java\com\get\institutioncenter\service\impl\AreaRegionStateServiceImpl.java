package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.AreaCountryMapper;
import com.get.institutioncenter.dao.AreaRegionMapper;
import com.get.institutioncenter.dao.AreaRegionStateMapper;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.institutioncenter.entity.AreaCountry;
import com.get.institutioncenter.entity.AreaRegion;
import com.get.institutioncenter.entity.AreaRegionState;
import com.get.institutioncenter.service.IAreaCountryService;
import com.get.institutioncenter.service.IAreaRegionStateService;
import com.get.institutioncenter.utils.MyStringUtils;
import com.get.institutioncenter.dto.AreaRegionDto;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务区域管理逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/7/28 14:45
 */
@Service
public class AreaRegionStateServiceImpl extends BaseServiceImpl<AreaRegionStateMapper, AreaRegionState> implements IAreaRegionStateService {
    @Resource
    private AreaRegionMapper areaRegionMapper;
    @Resource
    private AreaCountryMapper areaCountryMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IAreaCountryService areaCountryService;
    @Resource
    private AreaRegionStateMapper areaRegionStateMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    /**
     * 树状图
     *
     * @Date 14:49 2021/7/28
     * <AUTHOR>
     */
    @Override
    public List<AreaCountryVo> getTreeList() {
        LambdaQueryWrapper<AreaCountry> wrapper = new LambdaQueryWrapper();
        //获取国家 树的第一层
        wrapper.orderByDesc(AreaCountry::getViewOrder);
        List<AreaCountry> areaCountrys = areaCountryMapper.selectList(wrapper);
        return areaCountrys.stream().map(areaCountry -> BeanCopyUtils.objClone(areaCountry, AreaCountryVo::new)).collect(Collectors.toList());
    }


    /**
     * 根据关键字搜索
     *
     * @param areaRegionDto
     * @param page
     * @return
     * @
     */
    @Override
    public List<AreaRegionVo> getAreaRegionStates(AreaRegionDto areaRegionDto, Page page) {
        LambdaQueryWrapper<AreaRegion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(areaRegionDto.getFkAreaCountryId())) {
            lambdaQueryWrapper.eq(AreaRegion::getFkAreaCountryId, areaRegionDto.getFkAreaCountryId());
        }
        //公司查询
        if (GeneralTool.isNotEmpty(areaRegionDto.getFkCompanyId())) {
            lambdaQueryWrapper.eq(AreaRegion::getFkCompanyId, areaRegionDto.getFkCompanyId());
        }
        if (GeneralTool.isNotEmpty(areaRegionDto.getKeyWord())) {
            lambdaQueryWrapper.and(wrapper -> {
                wrapper.like(AreaRegion::getNum, areaRegionDto.getKeyWord());
                wrapper.or().like(AreaRegion::getName, areaRegionDto.getKeyWord());
                wrapper.or().like(AreaRegion::getNameChn, areaRegionDto.getKeyWord());
                wrapper.or().like(AreaRegion::getRemark, areaRegionDto.getKeyWord());
            });
        }
        lambdaQueryWrapper.orderByDesc(AreaRegion::getViewOrder);
        IPage<AreaRegion> pages = areaRegionMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        page.setAll((int) pages.getTotal());
        List<AreaRegion> areaRegions = pages.getRecords();
        List<AreaRegionVo> areaRegionVos = new ArrayList<>();
        if (GeneralTool.isEmpty(areaRegions)) {
            return areaRegionVos;
        }
        Map<Long, String> companyNamesByIds = new HashMap<>();
        Set<Long> companyIdSet = areaRegions.stream().map(AreaRegion::getFkCompanyId).collect(Collectors.toSet());
        companyIdSet.removeIf(Objects::isNull);
        if (GeneralTool.isNotEmpty(companyIdSet)) {
            companyNamesByIds = permissionCenterClient.getCompanyNamesByIds(companyIdSet).getData();
        }
        //获取所有国家id
        Set<Long> fkCountryIds = areaRegions.stream().map(AreaRegion::getFkAreaCountryId).collect(Collectors.toSet());
        //根据国家ids获取名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkCountryIds)) {
            countryNamesByIds = areaCountryService.getCountryNamesByIds(fkCountryIds);
        }
        for (AreaRegion areaRegion : areaRegions) {
            AreaRegionVo areaRegionVo = BeanCopyUtils.objClone(areaRegion, AreaRegionVo::new);
            areaRegionVo.setFkAreaCountryName(countryNamesByIds.get(areaRegionVo.getFkAreaCountryId()));
            StringBuilder sb = new StringBuilder();
            if (GeneralTool.isNotEmpty(areaRegionVo.getNameChn())) {
                sb.append(areaRegionVo.getName()).append("（").append(areaRegionVo.getNameChn()).append("）");
            } else {
                sb.append(areaRegionVo.getName());
            }
            areaRegionVo.setFullName(sb.toString());
            if (GeneralTool.isNotEmpty(companyNamesByIds) && GeneralTool.isNotEmpty(areaRegion.getFkCompanyId())) {
                areaRegionVo.setFkCompanyName(companyNamesByIds.get(areaRegion.getFkCompanyId()));
            }
            areaRegionVos.add(areaRegionVo);
        }
        return areaRegionVos;
    }

    @Override
    public void saveBatch(List<AreaRegionState> list) {
        if (GeneralTool.isNotEmpty(list)) {
            saveBatch(list,list.size());
        }
    }


    /**
     * 批量新增
     *
     * @param areaRegionDtos
     * @
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(ValidList<AreaRegionDto> areaRegionDtos) {
        if (GeneralTool.isEmpty(areaRegionDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (AreaRegionDto areaRegionDto : areaRegionDtos) {
            AreaRegion areaRegion = BeanCopyUtils.objClone(areaRegionDto, AreaRegion::new);
            utilService.updateUserInfoToEntity(areaRegion);
            areaRegionMapper.insertSelective(areaRegion);
            //自动生成编号
            areaRegion.setNum(MyStringUtils.getRegionStateNum(areaRegion.getId()));
            areaRegionMapper.updateById(areaRegion);
        }
    }

    /**
     * 删除
     *
     * @param id
     * @
     */
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        validateDelete(id);
        AreaRegion areaRegion = areaRegionMapper.selectById(id);
        if (areaRegion == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        areaRegionMapper.deleteById(id);
    }

    /**
     * 删除验证
     *
     * @param id
     */
    private void validateDelete(Long id) {
        LambdaQueryWrapper<AreaRegionState> wrapper = new LambdaQueryWrapper();
        wrapper.eq(AreaRegionState::getFkAreaStateId, id);
        List<AreaRegionState> areaRegionStates = areaRegionStateMapper.selectList(wrapper);
        if (GeneralTool.isNotEmpty(areaRegionStates)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("region_has_association"));
        }
    }


    /**
     * 更新
     *
     * @param areaRegionDto
     * @
     */
    @Override
    public void updateAreaState(AreaRegionDto areaRegionDto) {
        if (areaRegionDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        AreaRegion beforeAreaRegion = areaRegionMapper.selectById(areaRegionDto.getId());
        if (beforeAreaRegion == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AreaRegion areaRegion = BeanCopyUtils.objClone(areaRegionDto, AreaRegion::new);
        utilService.updateUserInfoToEntity(areaRegion);
        areaRegionMapper.updateById(areaRegion);
    }


    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @Override
    public AreaRegionVo findAreaRegionById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AreaRegion region = areaRegionMapper.selectById(id);
        AreaRegionVo areaRegionVo = BeanCopyUtils.objClone(region, AreaRegionVo::new);
        if (areaRegionVo != null && GeneralTool.isNotEmpty(areaRegionVo.getFkCompanyId())) {
            if (areaRegionVo.getFkCompanyId() == -1) {
                areaRegionVo.setFkCompanyName(TableEnum.GEOGRAPHICAL_DIVISION.value);
            } else {
                String data = permissionCenterClient.getCompanyNameById(areaRegionVo.getFkCompanyId()).getData();
                areaRegionVo.setFkCompanyName(data);
            }
            //语言
            areaRegionVo.setFkTableName(TableEnum.INSTITUTION_AREA_REGION.key);

        }
        return areaRegionVo;
    }

    /**
     * 获取当前国家的业务区域
     *
     * @param fkCountryId
     * @return
     * @
     */
    @Override
    public List<BaseSelectEntity> areaRegionSelectByCountryId(Long fkCountryId) {
        if (GeneralTool.isEmpty(fkCountryId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<BaseSelectEntity > areaRegions = areaRegionMapper.getByCountryId(fkCountryId, SecureUtil.getCompanyIds());
        if (GeneralTool.isEmpty(areaRegions)) {
            return new ArrayList<>();
        }
        return areaRegions;
    }

    /**
     * feign调用,根据ids获取对象集合
     *
     * @param ids
     * @return
     * @
     */
    @Override
    public Map<Long, AreaRegionVo> getAreaRegionDtoByIds(Set<Long> ids) {
        Map<Long, AreaRegionVo> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<AreaRegionVo> areaRegionVoByIds = areaRegionMapper.getAreaRegionDtoByIds(ids);
        if (GeneralTool.isEmpty(areaRegionVoByIds)) {
            return map;
        }
        //获取所有国家id
        Set<Long> fkCountryIds = areaRegionVoByIds.stream().map(AreaRegionVo::getFkAreaCountryId).collect(Collectors.toSet());
        //根据国家ids获取名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkCountryIds)) {
            countryNamesByIds = areaCountryService.getCountryNamesByIds(fkCountryIds);
        }
        for (AreaRegionVo areaRegionVoById : areaRegionVoByIds) {
            areaRegionVoById.setFkAreaCountryName(countryNamesByIds.get(areaRegionVoById.getFkAreaCountryId()));
            map.put(areaRegionVoById.getId(), areaRegionVoById);
        }
        return map;
    }


    /**
     * @Description: 上移下移
     * @Author: Jerry
     * @Date:9:57 2021/10/9
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<AreaRegionDto> areaRegionDtos) {
        if (GeneralTool.isEmpty(areaRegionDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        AreaRegion ro = BeanCopyUtils.objClone(areaRegionDtos.get(0), AreaRegion::new);
        Integer oneorder = ro.getViewOrder();
        AreaRegion rt = BeanCopyUtils.objClone(areaRegionDtos.get(1), AreaRegion::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        areaRegionMapper.updateById(ro);
        areaRegionMapper.updateById(rt);
    }

}

package com.get.financecenter.enums;

import com.get.core.mybatis.base.BaseSelectEntity;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum AssetTypesEnum {
//    余额方向：0借/1贷。资产、费用【借增贷减】。负债、权益、收入【贷增借减】
    BORROW(0, "借"),
    LOAN(1, "贷");

    private final Integer id;
    private final String name;

    AssetTypesEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }
    public String getName() {
        return name;
    }

    public static String getNameById(Integer id) {
        for (AssetTypesEnum assetType : AssetTypesEnum.values()) {
            if (assetType.id.equals(id)) {
                return assetType.name;
            }
        }
        return null;
    }

    public static List<BaseSelectEntity> asSelectList() {
        return Arrays.stream(values())
                .map(type ->{
                    BaseSelectEntity selectEntity = new BaseSelectEntity();
                    selectEntity.setId(Long.valueOf(type.getId()));
                    selectEntity.setName(type.getName());
                    selectEntity.setNameChn(type.getName());
                    selectEntity.setFullName(type.getName());
                    return selectEntity;
                })
                .collect(Collectors.toList());
    }
}

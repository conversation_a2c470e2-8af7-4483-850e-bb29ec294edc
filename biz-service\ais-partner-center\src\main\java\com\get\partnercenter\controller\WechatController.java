package com.get.partnercenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.partnercenter.dto.wechat.LinkAndQrCodeVo;
import com.get.partnercenter.dto.wechat.MinProgramQrCodeDto;
import com.get.partnercenter.service.WeChatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "微信服务管理")
@RestController
@RequestMapping("/wx")
public class WechatController {

    @Autowired
    private WeChatService weChatService;

    @ApiOperation(value = "获取小程序码")
    @PostMapping("/getQrCode")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.DETAIL, description = "华通伙伴/获取小程序码")
    public ResponseBo<Object> getQrCode(@RequestBody MinProgramQrCodeDto minProgramQrCodeDto) {
        return new ResponseBo(weChatService.getQrCode(Strings.EMPTY, minProgramQrCodeDto));
    }

    @ApiOperation(value = "获取小程序短链接")
    @PostMapping("/getUrlLink")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.DETAIL, description = "华通伙伴/获取小程序码")
    public ResponseBo<Object> getUrlLink(@RequestBody MinProgramQrCodeDto minProgramQrCodeDto) {
        return new ResponseBo(weChatService.getUrlLink(Strings.EMPTY, minProgramQrCodeDto));
    }

    @ApiOperation(value = "获取小程序短链接和小程序")
    @PostMapping("/getLinkAndQrCode")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.DETAIL, description = "华通伙伴/获取小程序码")
    public ResponseBo<LinkAndQrCodeVo> getLinkAndQrCode(@RequestBody MinProgramQrCodeDto minProgramQrCodeDto) {
        return new ResponseBo(weChatService.getLinkAndQrCode(Strings.EMPTY, minProgramQrCodeDto));
    }

}

package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.CompanyProfitAndLossItemMapper;
import com.get.financecenter.dao.CompanyProfitAndLossTemplateMapper;
import com.get.financecenter.dto.CompanyProfitAndLossItemDto;
import com.get.financecenter.entity.CompanyProfitAndLossItem;
import com.get.financecenter.entity.CompanyProfitAndLossTemplate;
import com.get.financecenter.enums.ShowModeEnum;
import com.get.financecenter.service.ICompanyProfitAndLossItemService;
import com.get.financecenter.utils.GetAccountingCodeNameUtils;
import com.get.financecenter.utils.GetNameUtils;
import com.get.financecenter.vo.CompanyProfitAndLossItemVo;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 公司利润表项目服务实现类
 */
@Service("financeCompanyProfitAndLossItemService")
public class CompanyProfitAndLossItemServiceImpl extends ServiceImpl<CompanyProfitAndLossItemMapper, CompanyProfitAndLossItem> implements ICompanyProfitAndLossItemService {

    @Resource
    private CompanyProfitAndLossItemMapper companyProfitAndLossItemMapper;

    @Resource
    private GetNameUtils getNameUtils;
    @Resource
    private GetAccountingCodeNameUtils getAccountingCodeNameUtils;
    @Autowired
    private UtilService utilService;

    @Resource
    private CompanyProfitAndLossTemplateMapper companyProfitAndLossTemplateMapper;
    @Autowired
    private MybatisPlusProperties mybatisPlusProperties;


    @Override
    public List<CompanyProfitAndLossItemVo> getCompanyProfitAndLossItem(CompanyProfitAndLossItemDto companyProfitAndLossItemDto, Page page) {
        if (GeneralTool.isEmpty(companyProfitAndLossItemDto.getFkCompanyId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        IPage<CompanyProfitAndLossItem> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<CompanyProfitAndLossItemVo> companyProfitAndLossItemVos =companyProfitAndLossItemMapper.getCompanyProfitAndLossItem(pages,companyProfitAndLossItemDto);
        page.setAll((int) pages.getTotal());
        if (GeneralTool.isEmpty(companyProfitAndLossItemVos)){
            return Collections.emptyList();
        }

        for (CompanyProfitAndLossItemVo companyProfitAndLossItemVo : companyProfitAndLossItemVos) {
            if (GeneralTool.isNotEmpty(companyProfitAndLossItemVo.getFkCompanyId())){
                getNameUtils.getCompanyNameById(companyProfitAndLossItemVo.getFkCompanyId());
            }
            if (GeneralTool.isNotEmpty(companyProfitAndLossItemVo.getShowMode())){
                companyProfitAndLossItemVo.setShowModeName(ShowModeEnum.getDescription(companyProfitAndLossItemVo.getShowMode()));
            }
            if (GeneralTool.isNotEmpty(companyProfitAndLossItemVo.getDirectionValue())){
                companyProfitAndLossItemVo.setDirectionValueName(companyProfitAndLossItemVo.getDirectionValue() == 1 ? "加" : "减");
            }
            if (GeneralTool.isNotEmpty(companyProfitAndLossItemVo.getFkAccountingItemId())){
                companyProfitAndLossItemVo.setAccountingItemName(getAccountingCodeNameUtils.setAccountingCodeName(companyProfitAndLossItemVo.getFkAccountingItemId()));
            }

        }
        return companyProfitAndLossItemVos;
    }

    @Override
    public List<Object> getShowMode() {
        return ShowModeEnum.getOptions();
    }

    @Override
    public Integer save(CompanyProfitAndLossItemDto companyProfitAndLossItemDto) {
        if (GeneralTool.isEmpty(companyProfitAndLossItemDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        CompanyProfitAndLossItem companyProfitAndLossItem = BeanCopyUtils.objClone(companyProfitAndLossItemDto, CompanyProfitAndLossItem::new);
        companyProfitAndLossItem.setViewOrder(companyProfitAndLossItemMapper.getMaxViewOrder());
        utilService.setCreateInfo(companyProfitAndLossItem);
        return companyProfitAndLossItemMapper.insert(companyProfitAndLossItem);
    }

    @Override
    public Integer updateById(CompanyProfitAndLossItemDto companyProfitAndLossItemDto) {
        if (GeneralTool.isEmpty(companyProfitAndLossItemDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        CompanyProfitAndLossItem companyProfitAndLossItemSearch = companyProfitAndLossItemMapper.selectById(companyProfitAndLossItemDto.getId());
        if (GeneralTool.isEmpty(companyProfitAndLossItemSearch)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        CompanyProfitAndLossItem companyProfitAndLossItem = BeanCopyUtils.objClone(companyProfitAndLossItemDto, CompanyProfitAndLossItem::new);
        utilService.updateUserInfoToEntity(companyProfitAndLossItem);

        LambdaUpdateWrapper<CompanyProfitAndLossItem> updateWrapper = new LambdaUpdateWrapper<CompanyProfitAndLossItem>()
                .eq(CompanyProfitAndLossItem::getId, companyProfitAndLossItemDto.getId())
                .set(CompanyProfitAndLossItem::getTitle, companyProfitAndLossItem.getTitle())
                .set(CompanyProfitAndLossItem::getFkAccountingItemId, companyProfitAndLossItem.getFkAccountingItemId())
                .set(CompanyProfitAndLossItem::getDirectionValue, companyProfitAndLossItem.getDirectionValue());
        return companyProfitAndLossItemMapper.update(companyProfitAndLossItem,updateWrapper);
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isNotEmpty(id)){
            CompanyProfitAndLossItem companyProfitAndLossItem = companyProfitAndLossItemMapper.selectById(id);
            if (GeneralTool.isEmpty(companyProfitAndLossItem)){
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
            }
            int i = companyProfitAndLossItemMapper.deleteById(id);
            if (i <= 0){
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sort(List<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }

        if (ids.size() != 2) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("sort_param_error"));
        }

        List<CompanyProfitAndLossItem> companyProfitAndLossItems = companyProfitAndLossItemMapper.selectBatchIds(ids);
        if (companyProfitAndLossItems.size() != ids.size()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        Map<Long, Integer> orderMap = companyProfitAndLossItems.stream().collect(Collectors.toMap(CompanyProfitAndLossItem::getId, CompanyProfitAndLossItem::getViewOrder));
        List<CompanyProfitAndLossItem> updateList = new ArrayList<>();

        for (int i = 0; i < ids.size(); i++) {
            Long currentId = ids.get(i);
            Long swapId = ids.get((i + 1) % ids.size());
            if (orderMap.containsKey(swapId)) {
                CompanyProfitAndLossItem entity = companyProfitAndLossItemMapper.selectById(currentId);
                if (entity.getViewOrder() == orderMap.get(swapId)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("inconsistent_data"));
                }
                entity.setViewOrder(orderMap.get(swapId));
                utilService.updateUserInfoToEntity(entity);
                updateList.add(entity);
            }
        }

        if (!updateList.isEmpty()) {
            companyProfitAndLossItemMapper.updateBatchById(updateList);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyTemplateToProject(Long fkCompanyId) {
        if (GeneralTool.isEmpty(fkCompanyId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        //  校验是否已创建数据
        List<CompanyProfitAndLossItem> companyProfitAndLossItemData = companyProfitAndLossItemMapper.selectList(new LambdaQueryWrapper<CompanyProfitAndLossItem>().eq(CompanyProfitAndLossItem::getFkCompanyId,fkCompanyId));
        if (GeneralTool.isNotEmpty(companyProfitAndLossItemData)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_profit_and_loss_item_data_already_exists"));
        }
        // 获取公司所有项目
        List<CompanyProfitAndLossTemplate> companyProfitAndLossTemplateList = companyProfitAndLossTemplateMapper.selectList(new LambdaQueryWrapper<CompanyProfitAndLossTemplate>());
        if (companyProfitAndLossTemplateList.isEmpty()){
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_replicable_template_data"));
        }
        List<CompanyProfitAndLossItem> companyProfitAndLossItemList = new ArrayList<>();
        for (CompanyProfitAndLossTemplate companyProfitAndLossTemplate : companyProfitAndLossTemplateList){
            CompanyProfitAndLossItem companyProfitAndLossItem = BeanCopyUtils.objClone(companyProfitAndLossTemplate, CompanyProfitAndLossItem::new);
            companyProfitAndLossItem.setFkCompanyId(fkCompanyId);
            utilService.updateUserInfoToEntity(companyProfitAndLossItem);
            companyProfitAndLossItemList.add(companyProfitAndLossItem);
        }
        companyProfitAndLossItemMapper.batchInsert(companyProfitAndLossItemList);
    }

    @Override
    public void movingOrder(Integer start, Integer end) {
        if (GeneralTool.isEmpty(start) || GeneralTool.isEmpty( end)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("sort_param_error"));
        }
        LambdaQueryWrapper<CompanyProfitAndLossItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(CompanyProfitAndLossItem::getViewOrder,start,end).orderByDesc(CompanyProfitAndLossItem::getViewOrder);
        }else {
            lambdaQueryWrapper.between(CompanyProfitAndLossItem::getViewOrder,end,start).orderByDesc(CompanyProfitAndLossItem::getViewOrder);
        }
        List<CompanyProfitAndLossItem> companyProfitAndLossItems = list(lambdaQueryWrapper);
        List<CompanyProfitAndLossItem> updateList = new ArrayList<>();
        if (end > start){
            int finalEnd = end;
            List<CompanyProfitAndLossItem> sortedList = Lists.newArrayList();
            CompanyProfitAndLossItem companyProfitAndLossItemLast = companyProfitAndLossItems.get(companyProfitAndLossItems.size() - 1);
            sortedList.add(companyProfitAndLossItemLast);
            companyProfitAndLossItems.remove(companyProfitAndLossItems.size()-1);
            sortedList.addAll(companyProfitAndLossItems);
            for (CompanyProfitAndLossItem receiptMethodType : sortedList) {
                receiptMethodType.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<CompanyProfitAndLossItem> sortedList = Lists.newArrayList();
            CompanyProfitAndLossItem companyProfitAndLossItemFirst = companyProfitAndLossItems.get(0);
            companyProfitAndLossItems.remove(0);
            sortedList.addAll(companyProfitAndLossItems);
            sortedList.add(companyProfitAndLossItemFirst);
            for (CompanyProfitAndLossItem receiptMethodType : sortedList) {
                receiptMethodType.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }

}


package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.dto.ContactPersonDto;
import com.get.institutioncenter.dto.ContractDto;
import com.get.institutioncenter.dto.InstitutionChannelCompanyQueryDto;
import com.get.institutioncenter.dto.InstitutionChannelCompanySearchDto;
import com.get.institutioncenter.dto.InstitutionDto;
import com.get.institutioncenter.dto.InstitutionProviderCompanyDto;
import com.get.institutioncenter.dto.InstitutionProviderDto;
import com.get.institutioncenter.dto.InstitutionProviderInstitutionDto;
import com.get.institutioncenter.dto.NewsDto;
import com.get.institutioncenter.dto.ProviderInstitutionCompanyUpdateDto;
import com.get.institutioncenter.dto.ProviderInstitutionRelationDto;
import com.get.institutioncenter.dto.query.ContractQueryDto;
import com.get.institutioncenter.dto.query.InstitutionProviderQueryDto;
import com.get.institutioncenter.dto.query.NewsQueryDto;
import com.get.institutioncenter.service.IInstitutionProviderCompanyService;
import com.get.institutioncenter.service.IInstitutionProviderService;
import com.get.institutioncenter.vo.ContactPersonVo;
import com.get.institutioncenter.vo.ContractVo;
import com.get.institutioncenter.vo.InstitutionProviderInstitutionChannelVo;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.institutioncenter.vo.InstitutionVo;
import com.get.institutioncenter.vo.NewsVo;
import com.get.institutioncenter.vo.ProviderInstitutionRelationVo;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Set;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: Sea
 * @create: 2020/7/14 12:06
 * @verison: 1.0
 * @description:
 */

@Api(tags = "学校提供商管理")
@RestController
@RequestMapping("/institution/institutionProvider")
public class InstitutionProviderController {
    @Resource
    private IInstitutionProviderService institutionProviderService;
    @Resource
    private IInstitutionProviderCompanyService institutionProviderCompanyService;


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校提供商管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(InstitutionProviderDto.Add.class) InstitutionProviderDto providerVo) {
        return SaveResponseBo.ok(institutionProviderService.addInstitutionProvider(providerVo));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/学校提供商管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionProviderService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 修改信息
     * @Param [providerVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/学校提供商管理/更新")
    @PostMapping("update")
    public ResponseBo<InstitutionProviderVo> update(@RequestBody @Validated(InstitutionProviderDto.Update.class) InstitutionProviderDto providerVo) {
        return UpdateResponseBo.ok(institutionProviderService.updateInstitutionProvider(providerVo));
    }


    //校验接口
    @ApiOperation(value = "校验业务国家接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LOGIN, description = "学校中心/学校提供商管理/校验业务国家接口")
    @PostMapping("checkAreaCountryId")
    public ResponseBo<Boolean> checkAreaCountryId(@RequestBody @Validated(InstitutionProviderDto.Update.class) InstitutionProviderDto providerVo) {
        return new ResponseBo<>(institutionProviderService.checkAreaCountryId(providerVo));

    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */

    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校提供商管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<InstitutionProviderVo> datas(@RequestBody SearchBean<InstitutionProviderQueryDto> page) {
        List<InstitutionProviderVo> datas = institutionProviderService.getProviderList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/学校提供商管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionProviderVo> detail(@PathVariable("id") Long id) {
        InstitutionProviderVo institutionProviderVo = institutionProviderService.findInstitutionProviderById(id);
        return new ResponseBo<>(institutionProviderVo);
    }


    @ApiOperation(value = "获取学校提供商名称", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/学校提供商管理/获取学校提供商名称")
    @GetMapping("info")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<String> info(@RequestParam("providerId")Long providerId,@RequestParam("channelId")Long channelId){
        return institutionProviderService.getInfo(providerId,channelId);
    }


//    /**
//     * 获取学校下面的提供商列表
//     *
//     * @param contactPersonVo
//     * @return
//     * @
//     */
//    @ApiOperation(value = "获取学校下面的提供商列表", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校提供商管理/获取学校下面的提供商列表")
//    @PostMapping("getInstitutionProvider")
//    public ResponseBo<InstitutionProviderVo> getInstitutionProvider(@RequestBody ContactPersonVo contactPersonVo)  {
//        List<InstitutionProviderVo> datas = institutionProviderService.getInstitutionProviderByInstitution(contactPersonVo.getFkInstitutionId(), contactPersonVo.getName());
//        return new ListResponseBo<>(datas);
//    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.ContractVo>
     * @Description: 合同列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "提供商合同列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校提供商管理/查询合同")
    @PostMapping("getContract")
    public ResponseBo<ContractVo> getContract(@RequestBody @Validated SearchBean<ContractQueryDto> page) {
        List<ContractVo> allContract = institutionProviderService.getContract(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(allContract, p);
    }

    /**
     * 新增提供商合同
     *
     * @param contractDto
     * @return
     * @
     */
    @ApiOperation(value = "新增提供商合同")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校提供商管理/新增合同")
    @PostMapping("addContract")
    public ResponseBo addContract(@RequestBody @Validated(ContractDto.Add.class) ContractDto contractDto) {
        return SaveResponseBo.ok(institutionProviderService.addContract(contractDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增提供商联系人
     * @Param [contactPersonDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增提供商联系人")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校提供商管理/新增联系人")
    @PostMapping("addProviderContactPerson")
    public ResponseBo addContactPerson(@RequestBody  ContactPersonDto contactPersonDto) {
        return SaveResponseBo.ok(institutionProviderService.addContactPerson(contactPersonDto));
    }

    /**
     * 批量标记续约中
     *
     * @param
     * @return
     * @
     */
    @ApiOperation(value = "批量标记续约中")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/学校提供商管理/批量标记续约中")
    @PostMapping("/batchUpdateProvidersToRenewing")
    public ResponseBo batchUpdateProvidersToRenewing(@RequestBody Set<Long> institutionProviderIds) {
        return institutionProviderService.batchUpdateProvidersToRenewing(institutionProviderIds);
    }



    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.ContactPersonDto>
     * @Description: 提供商联系人列表
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "提供商联系人列表")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校提供商管理/提供商联系人列表")
    @PostMapping("getProviderContactPersonDtos")
    public ResponseBo<ContactPersonVo> getInstitutionContactPersonDtos(@RequestBody SearchBean<ContactPersonDto> searchBean) {
        List<ContactPersonVo> contactPersonVos = institutionProviderService.getInstitutionContactPersonDtos(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(contactPersonVos, p);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增学校新闻
     * @Param [newsDto]
     * <AUTHOR>
     **/
    @ApiOperation(value = "新增学校新闻接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校提供商管理/新增新闻")
    @PostMapping("addNews")
    public ResponseBo addNews(@RequestBody @Validated(NewsDto.Add.class) NewsDto newsDto) {
        return SaveResponseBo.ok(institutionProviderService.addNews(newsDto));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.NewsVo>
     * @Description: 学校新闻列表数据
     * @Param [page]
     * <AUTHOR>
     **/
    @ApiOperation(value = "学校新闻列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校提供商管理/查询新闻")
    @PostMapping("getNewsDatas")
    public ResponseBo<NewsVo> getNewsDatas(@RequestBody SearchBean<NewsQueryDto> page) {
        List<NewsVo> datas = institutionProviderService.getNewsData(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 安全配置
     * @Param [validList]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存提供商-公司配置接口")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/提供商管理-公司绑定配置/保存配置")
    @PostMapping("editProviderCompanyRelation")
    public ResponseBo editProviderCompanyRelation(@RequestBody @Validated(InstitutionProviderCompanyDto.Add.class)
                                                          ValidList<InstitutionProviderCompanyDto> validList) {
        institutionProviderService.editProviderCompanyRelation(validList);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 安全配置
     * @Param [validList]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存提供商-学校配置接口")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/提供商管理-学校绑定配置/保存配置")
    @PostMapping("editProviderInstitutionRelation")
    public ResponseBo editProviderInstitutionRelation(@RequestBody @Validated(InstitutionProviderInstitutionDto.Add.class)
                                                              ValidList<InstitutionProviderInstitutionDto> validList) {
        institutionProviderService.editProviderInstitutionRelation(validList);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "提供商-学校安全配置")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/提供商管理-学校绑定配置/提供商-学校安全配置")
    @PostMapping("updateProviderInstitutionCompanyRelation")
    public ResponseBo updateProviderInstitutionCompanyRelation(@RequestBody ProviderInstitutionCompanyUpdateDto providerInstitutionCompanyUpdateDto) {
        institutionProviderService.updateProviderInstitutionCompanyRelation(providerInstitutionCompanyUpdateDto);
        return UpdateResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.CompanyTreeVo>
     * @Description: 回显提供商和公司的关系
     * @Param [fkProviderId]
     * <AUTHOR>
     */
    @ApiOperation(value = "回显提供商和公司的关系", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校提供商管理/提供商和公司的关系")
    @PostMapping("getProviderCompanyRelation/{providerId}")
    public ResponseBo<CompanyTreeVo> getProviderCompanyRelation(@PathVariable("providerId") Long providerId) {
        return new ListResponseBo<CompanyTreeVo>(institutionProviderService.getProviderCompanyRelation(providerId));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.ProviderInstitutionRelationVo>
     * @Description: 回显提供商和学校的关系
     * @Param [fkProviderId]
     * <AUTHOR>
     */
    @ApiOperation(value = "回显提供商和学校的关系", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校提供商管理/提供商和学校的关系")
    @PostMapping("getProviderInstitutionRelation")
    public ResponseBo<ProviderInstitutionRelationVo> getInstitutionProvider(@RequestBody SearchBean<ProviderInstitutionRelationDto> searchBean) {
        List<ProviderInstitutionRelationVo> data = institutionProviderService.getProviderInstitutionRelation(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(data, p);
    }

    /**
     * 学校列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "学校列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校提供商管理/查询学校")
    @PostMapping("getInstitution")
    public ResponseBo<InstitutionVo> getInstitution(@RequestBody SearchBean<InstitutionDto> page) {
        List<InstitutionVo> datas = institutionProviderService.getInstitution(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "导出学校列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校提供商管理/导出学校信息")
    @PostMapping("exportInstitutionInfo")
    public void exportInstitution(@RequestBody SearchBean<InstitutionDto> page, HttpServletResponse response) {
        institutionProviderService.exportInstitution(page.getData(),page,response);
    }

    /**
     * 学校提供商下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学校提供商下拉框数据", notes = "")
    @GetMapping("getInstitutionProviderList")
    public ResponseBo<InstitutionProviderVo> getInstitutionProviderList(@RequestParam(value = "companyId", required = false) Long companyId, @RequestParam(value = "name", required = false) String name) {
        List<InstitutionProviderVo> datas = institutionProviderService.getInstitutionProviderList(companyId, name);
        return new ListResponseBo<>(datas);
    }

    /**
     * 学校提供商下拉框数据,只是HTI
     *
     * @return
     * @
     */
    @ApiOperation(value = "学校提供商下拉框数据", notes = "")
    @VerifyLogin(IsVerify = false)
    @GetMapping("getInstitutionProviders")
    public ResponseBo<InstitutionProviderVo> getInstitutionProviders() {
        Long companyId = 3L;
        String name = "";
        List<InstitutionProviderVo> datas = institutionProviderService.getInstitutionProviders(companyId, name);
        return new ListResponseBo<>(datas);
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取所有学校提供商下拉框数据", notes = "")
    @GetMapping("getAllInstitutionSelection")
    public ListResponseBo<BaseSelectEntity> getAllInstitutionSelection(){
        return new ListResponseBo<>(institutionProviderService.getAllInstitutionSelection());
    }

    /**
     * 根据国家获取学校提供商下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据国家获取学校提供商下拉框数据", notes = "")
    @GetMapping("getInstitutionProviderListByCountry")
    public ResponseBo<BaseSelectEntity> getInstitutionProviderListByCountry(@RequestParam(value = "countryId") Long countryId) {
        List<BaseSelectEntity> datas = institutionProviderService.getInstitutionProviderListByCountry(countryId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 根据学校获取绑定的学校提供商下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据学校获取绑定的学校提供商下拉框数据", notes = "")
    @GetMapping("getInstitutionProviderListByInstitution/{institutionId}")
    public ResponseBo<BaseSelectEntity> getInstitutionProviderListByInstitution(@PathVariable("institutionId") Long institutionId, @RequestParam("studentCompanyId") Long studentCompanyId) {
        List<BaseSelectEntity> datas = institutionProviderService.getInstitutionProviderListByInstitution(institutionId, studentCompanyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * feign调用 根据输入的学校提供商名称 模糊查询对应的学校提供商id
     *
     * @param institutionProviderName
     * @return
     * @
     */
/*    @ApiIgnore
    @GetMapping("getInstitutionProviderIds")
    public List<Long> getInstitutionProviderIds(@RequestParam String institutionProviderName)  {
        return institutionProviderService.getInstitutionProviderIdsByName(institutionProviderName);
    }*/

    /**
     * feign调用 根据输入的学校提供商id 查询对应的学校提供商名称
     *
     * @param id
     * @return
     * @
     */
  /*  @ApiIgnore
    @GetMapping("getInstitutionProviderName")
    public String getInstitutionProviderName(@RequestParam(required = false) Long id)  {
        return institutionProviderService.getInstitutionProviderNameById(id);
    }
*/
    /**
     * @return java.util.Map<java.lang.Long, java.lang.String>
     * @Description :fegin调用 根据学校提供商ids 查询名称map
     * @Param [ids]
     * <AUTHOR>
     */
/*    @ApiIgnore
    @PostMapping("getInstitutionProviderNamesByIds")
    public Map<Long, String> getInstitutionProviderNamesByIds(@RequestBody Set<Long> ids) {
        return institutionProviderService.getInstitutionProviderNamesByIds(ids);
    }*/


    /**
     * feign调用 根据学校提供商id查找学校提供商类型
     *
     * @param id
     * @return
     */
  /*  @ApiIgnore
    @GetMapping(value = "getInstitutionProviderType/{id}")
    public String getInstitutionProviderType(@PathVariable("id") Long id)  {
        return institutionProviderService.getInstitutionProviderTypeById(id);
    }*/

    /**
     * feign调用 下拉
     *
     * @param
     * @return
     */
/*    @VerifyPermission(IsVerify = false)
    @ApiIgnore
    @GetMapping("getInstitutionProviderSelect")
    public ResponseBo<BaseSelectEntity> getInstitutionProviderSelect(@RequestParam(value = "companyId") Long companyId)  {
        List<BaseSelectEntity> datas = institutionProviderService.getInstitutionProviderSelect(companyId);
        return new ListResponseBo<>(datas);
    }*/

    /**
     * 根据提供商id获取业务国家下拉框数据
     *
     * @Date 10:31 2021/9/9
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据提供商id获取业务国家下拉框数据", notes = "")
    @GetMapping("getCountrySelectByInstitutionProvider")
    public ResponseBo<BaseSelectEntity> getCountrySelectByInstitutionProvider(@RequestParam(value = "providerId") Long providerId) {
        List<BaseSelectEntity> datas = institutionProviderService.getCountrySelectByInstitutionProvider(providerId);
        return new ListResponseBo<>(datas);
    }


    /**
     * @Description: feign调用 根据提供商ids查询公司
     * @Author: Jerry
     * @Date:11:21 2021/11/20
     */
 /*   @ApiIgnore
    @GetMapping("getCompanyIdsByProviderIds")
    public Map<Long,Set<Long>> getCompanyIdsByProviderIds(@RequestParam("providerIds") Set<Long> providerIds)  {
        return institutionProviderCompanyService.getRelationByProviderIds(providerIds);
    }*/

    /**
     * feign调用 获取所有的渠道Map
     *
     * @param
     * @return
     */
/*    @ApiIgnore
    @GetMapping("getInstitutionProviderChannel")
    public Map<Long, String> getInstitutionProviderChannel()  {
        return institutionProviderService.getInstitutionProviderChannel();
    }*/

    /**
     * feign调用 获取渠道名By id
     *
     * @param
     * @return
     */
 /*   @ApiIgnore
    @GetMapping("getInstitutionProviderChannelById/{id}")
    public String getInstitutionProviderChannelById(@PathVariable("id") Long id)  {
        return institutionProviderService.getInstitutionProviderChannelById(id);
    }*/

    /**
     * 根据渠道名称获取提供商ids
     *
     * @param
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据渠道集团获取提供商ids", notes = "")
    @GetMapping("getInstitutionProviderIdsByChannelGroup")
    public List<Long> getInstitutionProviderIdsByChannelGroup(@RequestParam(value = "groupId", required = false) Long groupId, @RequestParam(value = "channelId", required = false) Long channelId) {
        List<Long> datas = institutionProviderService.getInstitutionProviderIdsByChannelGroup(groupId, channelId);
        return datas;
    }

    /**
     * @author: Neil
     * @description:
     * @date: 2022/4/27 10:50
     * 根据提供商名称或者渠道名称获取学校提供商信息和渠道信息
     */
    @PostMapping("getInstitutionProviderByName")
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据提供商名称或者渠道名称获取学校提供商信息和渠道信息", notes = "")
    public ResponseBo<InstitutionProviderInstitutionChannelVo> getInstitutionProviderByName(@RequestBody InstitutionChannelCompanyQueryDto queryVo) {
        List<InstitutionProviderInstitutionChannelVo> institutionProviderByName = institutionProviderService.getInstitutionProviderByName(queryVo);
        return new ListResponseBo<>(institutionProviderByName);
    }


    /**
     * @author: Neil
     * @description:
     * @date: 2022/4/27 10:50
     * 修改合作绑定状态
     */
    @GetMapping("updateBindingActive")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/提供商管理-学校绑定配置/修改合作绑定状态")
    @ApiOperation(value = "修改合作绑定状态", notes = "")
    public ResponseBo updateBindingActive(@RequestParam("institutionId") Long institutionId,@RequestParam("institutionProviderId") Long institutionProviderId,@RequestParam("isBindingActive") Integer isBindingActive) {
        institutionProviderService.updateBindingActive(institutionId,institutionProviderId,isBindingActive);
        return ResponseBo.ok();
    }

    /**
     * @author: Neil
     * @description: 根据提供商名称获取学校提供商信息信息(百度式搜索)
     * @date: 2023/1/5 14:53
     * @return
     */
    @PostMapping("getInstitutionProviderByProviderName")
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据提供商名称获取学校提供商信息信息(百度式搜索)", notes = "")
    public ResponseBo<InstitutionProviderInstitutionChannelVo> getInstitutionProviderByProviderName(@RequestBody InstitutionChannelCompanySearchDto queryVo) {
        List<InstitutionProviderInstitutionChannelVo> institutionProviderByName = institutionProviderService.getInstitutionProviderByProviderName(queryVo);
        return new ListResponseBo<>(institutionProviderByName);
    }
}

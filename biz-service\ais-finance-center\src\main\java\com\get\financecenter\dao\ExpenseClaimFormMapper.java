package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.vo.ExpenseClaimFormVo;
import com.get.financecenter.entity.ExpenseClaimForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ExpenseClaimFormMapper extends BaseMapper<ExpenseClaimForm> {


    /**
     * 查询父单
     *
     * @param id
     * @return
     */
    ExpenseClaimFormVo getExistParentId(@Param("id") Long id);
}
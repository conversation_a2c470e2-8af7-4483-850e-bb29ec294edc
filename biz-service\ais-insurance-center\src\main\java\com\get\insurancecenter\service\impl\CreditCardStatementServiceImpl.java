package com.get.insurancecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.insurancecenter.dto.card.TradeRecordDto;
import com.get.insurancecenter.entity.CreditCardStatement;
import com.get.insurancecenter.mapper.CreditCardStatementMapper;
import com.get.insurancecenter.service.CreditCardStatementService;
import com.get.insurancecenter.vo.card.TradeRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class CreditCardStatementServiceImpl extends ServiceImpl<CreditCardStatementMapper, CreditCardStatement> implements CreditCardStatementService {

    @Autowired
    private CreditCardStatementMapper creditCardStatementMapper;

    @Override
    public List<TradeRecordVo> tradeRecordPage(TradeRecordDto params, Page page) {
        IPage<TradeRecordVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<TradeRecordVo> result = creditCardStatementMapper.selectTradeRecordPage(pages, params);
        int totalCount = (int) pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return result;
    }
}

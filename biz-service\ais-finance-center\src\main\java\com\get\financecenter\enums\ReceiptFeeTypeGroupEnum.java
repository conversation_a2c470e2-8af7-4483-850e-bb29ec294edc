package com.get.financecenter.enums;

import com.get.financecenter.vo.ReceiptFeeTypeVo;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;

@Getter
public enum ReceiptFeeTypeGroupEnum {

    STAFF("m_staff", "staff（员工）"),

//    PROVIDER("m_provider", "provider（提供商）"),

//    STUDENT("m_student","student（学生）"),
    COMPANY("m_company", "company（公司）"),

    STUDENT_SERVICE_FEE("m_student_service_fee", "studentServiceFee（学生服务费）");

    /**
     * 关联项关联类型Key（目标类型表名）
     */
    public String typeGroupKey;
    /**
     * 描述
     */
    public String name;

    ReceiptFeeTypeGroupEnum(String typeGroupKey, String name) {
        this.typeGroupKey = typeGroupKey;
        this.name = name;
    }

    public static String getNameByTypeGroupKey(String typeGroupKey) {
        for (ReceiptFeeTypeGroupEnum value : ReceiptFeeTypeGroupEnum.values()) {
            if (value.getTypeGroupKey().equals(typeGroupKey)) {
                return value.getName();
            }
        }
        return null;
    }

    public static List<ReceiptFeeTypeVo> getOptions() {
        return Arrays.stream(values())
                .map(type ->{
                    ReceiptFeeTypeVo selectEntity = new ReceiptFeeTypeVo();
                    selectEntity.setTypeGroupKey(type.typeGroupKey);
                    selectEntity.setTypeGroupKeyName(type.name);
                    return selectEntity;
                })
                .collect(Collectors.toList());
    }
}

package com.get.examcenter.mapper.exam;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.examcenter.vo.ExaminationQuestionVo;
import com.get.examcenter.entity.ExaminationQuestion;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DS("examdb")
public interface ExaminationQuestionMapper extends BaseMapper<ExaminationQuestion> {
    int insert(ExaminationQuestion record);

    int insertSelective(ExaminationQuestion record);

    int updateByPrimaryKeySelective(ExaminationQuestion record);

    int updateByPrimaryKey(ExaminationQuestion record);

    //获取指定数量的题目
    List<ExaminationQuestionVo> getExaminationQuestionByIdsAndCount(@Param("questionIds") Set<Long> questionIds, @Param("questionCount") Integer questionCount);

    //考题总数
    Integer getQuestionSumCount();
}
package com.get.examcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.aisplatformcenterap.feign.IPlatformCenterClient;
import com.get.aisplatformcenterap.vo.UserInfoVo;
import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.examcenter.vo.UserExaminationQuestionScoreVo;
import com.get.examcenter.vo.UserexaminationPaperScoreVo;
import com.get.examcenter.entity.Examination;
import com.get.examcenter.entity.ExaminationAnswer;
import com.get.examcenter.entity.ExaminationPaper;
import com.get.examcenter.entity.ExaminationQuestion;
import com.get.examcenter.entity.UserExaminationQuestionScore;
import com.get.examcenter.entity.UserStaffBd;
import com.get.examcenter.entity.UserexaminationPaperScore;
import com.get.examcenter.mapper.appexam.UserExaminationQuestionScoreMapper;
import com.get.examcenter.mapper.appexam.UserStaffBdMapper;
import com.get.examcenter.mapper.appexam.UserexaminationPaperScoreMapper;
import com.get.examcenter.mapper.exam.ExaminationAnswerMapper;
import com.get.examcenter.service.ExaminationPaperService;
import com.get.examcenter.service.ExaminationService;
import com.get.examcenter.service.IExaminationQuestionService;
import com.get.examcenter.service.IQuestionTypeService;
import com.get.examcenter.service.UserService;
import com.get.examcenter.dto.UserExaminationQuestionScoreDto;
import com.get.examcenter.dto.UserDto;
import com.get.examcenter.dto.UserexaminationPaperScoreDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * Created by Jerry.
 * Time: 11:14
 * Date: 2021/8/25
 * Description:考生管理实现类
 */
@Service
public class UserServiceImpl implements UserService {

    @Resource
    private UtilService utilService;
    @Resource
    private UserExaminationQuestionScoreMapper userExaminationQuestionScoreMapper;
    @Resource
    private UserexaminationPaperScoreMapper userexaminationPaperScoreMapper;
    @Resource
    private ExaminationAnswerMapper examinationAnswerMapper;
//    @Resource
//    private IPlatformConfigCenterClient platformConfigCenterClient;
    @Resource
    private IPlatformCenterClient platformCenterClient;
    @Resource
    private IExaminationQuestionService examinationQuestionService;
    @Resource
    private IQuestionTypeService questionTypeService;
    @Resource
    private ExaminationService examinationService;
    @Resource
    private ExaminationPaperService examinationPaperService;
    @Resource
    private UserStaffBdMapper userStaffBdMapper;

    /**
     * @Description: 生成考试记录（获取考题的时候已经生成了记录，所以这里只对原记录做更新）
     * @Author: Jerry
     * @Date:11:14 2021/8/25
     */
    @Override
    public UserexaminationPaperScoreVo generateExaminationRecords(String optGuid) {
        if (GeneralTool.isEmpty(optGuid)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //根据操作id获取考试记录
//        Example example = new Example(UserexaminationPaperScore.class);
//        example.createCriteria().andEqualTo("optGuid", optGuid);
        List<UserexaminationPaperScore> userexaminationPaperScores = userexaminationPaperScoreMapper.selectList(Wrappers.<UserexaminationPaperScore>lambdaQuery().eq(UserexaminationPaperScore::getOptGuid, optGuid));
        if (GeneralTool.isEmpty(userexaminationPaperScores)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        UserexaminationPaperScore userexaminationPaperScore = userexaminationPaperScores.get(0);
        //统计当次考试的总分数
        List<UserexaminationPaperScoreVo> scoreAndUseTimeByOptGuid = userExaminationQuestionScoreMapper.getScoreAndUseTimeByOptGuid(optGuid);
        if (GeneralTool.isEmpty(scoreAndUseTimeByOptGuid)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        UserexaminationPaperScoreVo userexaminationPaperScoreVo = scoreAndUseTimeByOptGuid.get(0);
        userexaminationPaperScore.setScore(userexaminationPaperScoreVo.getScore());
        userexaminationPaperScore.setUseTime(userexaminationPaperScoreVo.getUseTime());
        utilService.updateUserInfoToEntity(userexaminationPaperScore);
        userexaminationPaperScoreMapper.updateByPrimaryKey(userexaminationPaperScore);

        UserexaminationPaperScoreVo userexaminationPaperScoreVoReturn = BeanCopyUtils.objClone(userexaminationPaperScore, UserexaminationPaperScoreVo::new);
        Long fkExaminationId = userexaminationPaperScoreVoReturn.getFkExaminationId();
        Map<Long, Examination> examinationByExaminationIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkExaminationId)) {
            Set<Long> fkExaminationIds = new HashSet<>();
            fkExaminationIds.add(fkExaminationId);
            examinationByExaminationIds = examinationService.getExaminationByExaminationIds(fkExaminationIds);
            //考试名称
            userexaminationPaperScoreVoReturn.setFkExaminationName(examinationByExaminationIds.get(fkExaminationIds).getName());
            //考试编号
            userexaminationPaperScoreVoReturn.setFkExaminationNum(examinationByExaminationIds.get(fkExaminationIds).getNum());
        }
        return userexaminationPaperScoreVoReturn;
    }


    /**
     * @Description: 生成答题记录
     * @Author: Jerry
     * @Date:11:15 2021/8/25
     */
    @Override
    public String generateAnswerRecords(UserExaminationQuestionScoreDto userExaminationQuestionScoreDto) {
        if (GeneralTool.isEmpty(userExaminationQuestionScoreDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        UserExaminationQuestionScore userExaminationQuestionScore = BeanCopyUtils.objClone(userExaminationQuestionScoreDto, UserExaminationQuestionScore::new);
        //本题分数
        Integer score = userExaminationQuestionScore.getScore();
        userExaminationQuestionScore.setScore(0);
        //查询当前问题的答案，如果答案匹配，则获取这个问题对应的分数
//        Example example = new Example(ExaminationAnswer.class);
//        example.createCriteria().andEqualTo("fkExaminationQuestionId", userExaminationQuestionScore.getFkExaminationQuestionId());
        //问题的所有选项
        List<ExaminationAnswer> examinationAnswers = examinationAnswerMapper.selectList(Wrappers.<ExaminationAnswer>lambdaQuery().eq(ExaminationAnswer::getFkExaminationQuestionId, userExaminationQuestionScore.getFkExaminationQuestionId()));
        StringJoiner sj = new StringJoiner(",");
        if (GeneralTool.isNotEmpty(examinationAnswers)) {
            //获取所有的正确答案
            examinationAnswers.stream().filter(examinationAnswer -> examinationAnswer.getIsRightAnswer()).
                    forEach(examinationAnswer -> sj.add(examinationAnswer.getId().toString()));
            if (GeneralTool.isNotEmpty(userExaminationQuestionScore.getFkExaminationAnswerIds()) && sj.toString().equals(userExaminationQuestionScore.getFkExaminationAnswerIds())) {
                //答案匹配，获取对应的分数
                userExaminationQuestionScore.setScore(score);
            }
        }
        utilService.updateUserInfoToEntity(userExaminationQuestionScore);
        userExaminationQuestionScoreMapper.insertSelective(userExaminationQuestionScore);
        return sj.toString();
    }

    /**
     * @Description: 考生列表
     * @Author: Jerry
     * @Date:11:26 2021/8/27
     */
    @Override
    public List<UserexaminationPaperScoreVo> getUserExaminationPaperScore(UserDto userDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        Set<Long> userIds = null;
        //根据考生姓名或者手机号查询考生
        if (GeneralTool.isNotEmpty(userDto.getPhoneNumber()) || GeneralTool.isNotEmpty(userDto.getUserName())) {
//            userIds = platformConfigCenterClient.getUserIdsByNameOrMobile(userVo.getUserName(), userVo.getPhoneNumber());
            Result<Set<Long>> result = platformCenterClient.getUserIdsByNameOrMobile(userDto.getUserName(), userDto.getPhoneNumber());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                userIds = result.getData();
            }
            //查询不出结果
            if (GeneralTool.isEmpty(userIds)) {
                return new ArrayList<>();
            }
        }
        IPage<UserexaminationPaperScoreVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<UserexaminationPaperScoreVo> userExaminationPaperScore = userexaminationPaperScoreMapper.getUserExaminationPaperScore(pages, userIds);
        page.setAll((int) pages.getTotal());
        if (GeneralTool.isEmpty(userExaminationPaperScore)) {
            //查询不出结果
            return new ArrayList<>();
        }
//        page.restPage(userExaminationPaperScore);
        //用户ids
        Set<Long> fkUserIds = userExaminationPaperScore.stream().map(UserexaminationPaperScoreVo::getFkUserId).collect(Collectors.toSet());
        Map<Long, UserInfoVo> userInfoDtoByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkUserIds)) {
//            userInfoDtoByIds = platformConfigCenterClient.getUserInfoDtoByIds(fkUserIds);
            Result<Map<Long, UserInfoVo>> result = platformCenterClient.getUserInfoDtoByIds(fkUserIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                userInfoDtoByIds = result.getData();
            }
        }
        for (UserexaminationPaperScoreVo userexaminationPaperScoreVo : userExaminationPaperScore) {
            UserInfoVo userInfoVo = userInfoDtoByIds.get(userexaminationPaperScoreVo.getFkUserId());
            if (GeneralTool.isEmpty(userInfoVo)) {
                continue;
            }
            userexaminationPaperScoreVo.setFkUserName(userInfoVo.getWechatNickname());
            userexaminationPaperScoreVo.setPhoneNumber(userInfoVo.getMobile());
        }
        return userExaminationPaperScore;
    }

    /**
     * @Description: 答题记录列表
     * @Author: Jerry
     * @Date:12:14 2021/8/27
     */
    @Override
    public List<UserExaminationQuestionScoreVo> answerRecordsList(UserexaminationPaperScoreDto data, SearchBean<UserexaminationPaperScoreDto> page) {
        if (GeneralTool.isEmpty(data.getOptGuid())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        List<UserExaminationQuestionScoreVo> userExaminationQuestionScoreVos = new ArrayList<>();
//        Example example = new Example(UserExaminationQuestionScore.class);
//        example.createCriteria().andEqualTo("optGuid", data.getOptGuid());
        LambdaQueryWrapper<UserExaminationQuestionScore> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserExaminationQuestionScore::getOptGuid, data.getOptGuid());
        IPage<UserExaminationQuestionScore> iPage = userExaminationQuestionScoreMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<UserExaminationQuestionScore> userExaminationQuestionScores = iPage.getRecords();
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(userExaminationQuestionScores)) {
            return userExaminationQuestionScoreVos;
        }
//        page.restPage(userExaminationQuestionScores);
        //获取所有的答案ids
        Set<Long> fkAnswerIds = new HashSet<>();
        //获取所有的问题ids
        Set<Long> fkExaminationQuestionIds = new HashSet<>();
        Map<Long, ExaminationQuestion> examinationQuestionByQuestionIds = new HashMap<>();
        Map<Long, String> answerNamesByAnswerIds = new HashMap<>();
        Map<Long, String> namesByQuestionTypeIds = new HashMap<>();
        for (UserExaminationQuestionScore userExaminationQuestionScore : userExaminationQuestionScores) {
            if (GeneralTool.isNotEmpty(userExaminationQuestionScore.getFkExaminationQuestionId())) {
                fkExaminationQuestionIds.add(userExaminationQuestionScore.getFkExaminationQuestionId());
            }
            if (GeneralTool.isNotEmpty(userExaminationQuestionScore.getFkExaminationAnswerIds())) {
                String[] answerIds = userExaminationQuestionScore.getFkExaminationAnswerIds().split(",");
                for (String answerId : answerIds) {
                    fkAnswerIds.add(Long.valueOf(answerId));
                }
            }
        }
        //根据问题ids获取对象
        examinationQuestionByQuestionIds = examinationQuestionService.getExaminationQuestionByQuestionIds(fkExaminationQuestionIds);
        //根据答案ids获取名称
        answerNamesByAnswerIds = examinationQuestionService.getAnswerNamesByAnswerIds(fkAnswerIds);
        //获取所有考题下的考题类型ids
        if (GeneralTool.isNotEmpty(examinationQuestionByQuestionIds)) {
            Set<Long> fkQuestionTypeIds = new HashSet<>();
            for (Map.Entry<Long, ExaminationQuestion> examinationQuestion : examinationQuestionByQuestionIds.entrySet()) {
                Long fkQuestionTypeId = examinationQuestion.getValue().getFkQuestionTypeId();
                if (GeneralTool.isNotEmpty(fkQuestionTypeId)) {
                    fkQuestionTypeIds.add(fkQuestionTypeId);
                }
            }
            if (GeneralTool.isNotEmpty(fkQuestionTypeIds)) {
                //根据考题类型ids获取名称
                namesByQuestionTypeIds = questionTypeService.getNamesByQuestionTypeIds(fkQuestionTypeIds);
            }
        }
        for (UserExaminationQuestionScore userExaminationQuestionScore : userExaminationQuestionScores) {
            UserExaminationQuestionScoreVo examinationQuestionScoreDto = new UserExaminationQuestionScoreVo();
            examinationQuestionScoreDto.setScore(userExaminationQuestionScore.getScore());
            examinationQuestionScoreDto.setUseTime(userExaminationQuestionScore.getUseTime());
            ExaminationQuestion examinationQuestion = examinationQuestionByQuestionIds.get(userExaminationQuestionScore.getFkExaminationQuestionId());
            if (GeneralTool.isNotEmpty(examinationQuestion)) {
                examinationQuestionScoreDto.setNum(examinationQuestion.getNum());
                examinationQuestionScoreDto.setFkQuestionTypeName(namesByQuestionTypeIds.get(examinationQuestion.getFkQuestionTypeId()));
                examinationQuestionScoreDto.setQuestion(examinationQuestion.getQuestion());
            }
            if (GeneralTool.isNotEmpty(userExaminationQuestionScore.getFkExaminationAnswerIds())) {
                //拆分开多个答案获取对应的名称
                StringJoiner sj = new StringJoiner("、");
                String[] answerIds = userExaminationQuestionScore.getFkExaminationAnswerIds().split(",");
                for (String answerId : answerIds) {
                    if (GeneralTool.isNotEmpty(answerNamesByAnswerIds.get(Long.valueOf(answerId)))) {
                        sj.add(answerNamesByAnswerIds.get(Long.valueOf(answerId)));
                    }
                }
                examinationQuestionScoreDto.setAnswer(sj.toString());
            }
            userExaminationQuestionScoreVos.add(examinationQuestionScoreDto);
        }
        return userExaminationQuestionScoreVos;
    }

    /**
     * @Description: 考试记录
     * @Author: Jerry
     * @Date:16:13 2021/8/27
     */
    @Override
    public List<UserexaminationPaperScoreVo> getUserPaperData(UserexaminationPaperScoreDto data, SearchBean<UserexaminationPaperScoreDto> page) {
        if (GeneralTool.isEmpty(data.getFkUserId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        Example example = new Example(UserexaminationPaperScore.class);
//        example.createCriteria().andEqualTo("fkUserId", data.getFkUserId()).andIsNotNull("score");
//        example.orderBy("gmtModified").desc();
//        List<UserexaminationPaperScore> userexaminationPaperScores = userexaminationPaperScoreMapper.selectByExample(example);
        IPage<UserexaminationPaperScore> pages = userexaminationPaperScoreMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), Wrappers.<UserexaminationPaperScore>lambdaQuery()
                .eq(UserexaminationPaperScore::getFkUserId, data.getFkUserId()).orderByDesc(UserexaminationPaperScore::getGmtModified));
        List<UserexaminationPaperScore> userexaminationPaperScores = pages.getRecords();
        page.setAll((int) pages.getTotal());
        if (GeneralTool.isEmpty(userexaminationPaperScores)) {
            return new ArrayList<>();
        }

//        page.restPage(userexaminationPaperScores);
        //找出全部Examination考试id
        Set<Long> fkExaminationIds = userexaminationPaperScores.stream().map(UserexaminationPaperScore::getFkExaminationId).collect(Collectors.toSet());
        Map<Long, Examination> examinationByExaminationIds = new HashMap<>();
        //找出全部考场id
        Set<Long> fkExaminationPaperIds = userexaminationPaperScores.stream().map(UserexaminationPaperScore::getFkExaminationPaperId).collect(Collectors.toSet());
        Map<Long, ExaminationPaper> examinationPaperByExaminationPaperIds = new HashMap<>();

        if (GeneralTool.isNotEmpty(fkExaminationIds)) {
            //根据考试ids获取对象
            examinationByExaminationIds = examinationService.getExaminationByExaminationIds(fkExaminationIds);
        }
        if (GeneralTool.isNotEmpty(fkExaminationPaperIds)) {
            //根据考卷ids获取对象
            examinationPaperByExaminationPaperIds = examinationPaperService.getExaminationPaperByExaminationPaperIds(fkExaminationPaperIds);
        }

        List<UserexaminationPaperScoreVo> userexaminationPaperScoreVos = new ArrayList<>();
        for (UserexaminationPaperScore userexaminationPaperScore : userexaminationPaperScores) {
            UserexaminationPaperScoreVo userexaminationPaperScoreVo = BeanCopyUtils.objClone(userexaminationPaperScore, UserexaminationPaperScoreVo::new);
            //获取对应的考试对象
            Examination examination = examinationByExaminationIds.get(userexaminationPaperScoreVo.getFkExaminationId());
            //获取对应的考卷对象
            ExaminationPaper examinationPaper = examinationPaperByExaminationPaperIds.get(userexaminationPaperScoreVo.getFkExaminationPaperId());
            if (GeneralTool.isNotEmpty(examination)) {
                userexaminationPaperScoreVo.setFkExaminationNum(examination.getNum());
                userexaminationPaperScoreVo.setFkExaminationName(examination.getName());
            }
            if (GeneralTool.isNotEmpty(examinationPaper)) {
                userexaminationPaperScoreVo.setFkPaperName(examinationPaper.getName());
                userexaminationPaperScoreVo.setFkPaperNum(examinationPaper.getNum());
            }
            userexaminationPaperScoreVos.add(userexaminationPaperScoreVo);
        }
        return userexaminationPaperScoreVos;
    }


    /**
     * @Description: feign调用 根据员工ids（BD）获取用户ids
     * @Author: Jerry
     * @Date:14:43 2021/8/30
     */
    @Override
    public Set<Long> getUserIdsByStaffIds(Set<Long> fkStaffIds) {
        if (GeneralTool.isEmpty(fkStaffIds)) {
            return null;
        }
//        Example example = new Example(UserStaffBd.class);
//        example.createCriteria().andIn("fkStaffId",fkStaffIds);
//        List<UserStaffBd> userStaffBds = userStaffBdMapper.selectByExample(example);
        List<UserStaffBd> userStaffBds = userStaffBdMapper.selectList(Wrappers.<UserStaffBd>lambdaQuery().in(UserStaffBd::getFkStaffId, fkStaffIds));
        if (GeneralTool.isEmpty(userStaffBds)) {
            return null;
        }
        return userStaffBds.stream().map(UserStaffBd::getFkUserId).collect(Collectors.toSet());
    }
}

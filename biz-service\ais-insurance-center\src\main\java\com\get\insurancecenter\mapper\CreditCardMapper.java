package com.get.insurancecenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.insurancecenter.dto.card.CreditCardPageDto;
import com.get.insurancecenter.entity.CreditCard;
import com.get.insurancecenter.vo.card.BankVo;
import com.get.insurancecenter.vo.card.CreateCardPageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
@Mapper
public interface CreditCardMapper extends BaseMapper<CreditCard> {

    /**
     * 信用卡列表-分页
     * @param page
     * @param param
     * @return
     */
    List<CreateCardPageVo> selectCreateCardPage(IPage<CreateCardPageVo> page, @Param("param") CreditCardPageDto param);

    /**
     * 银行列表
     * @return
     */
    List<BankVo> selectBankList();

}


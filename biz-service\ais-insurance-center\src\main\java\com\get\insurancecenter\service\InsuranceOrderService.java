package com.get.insurancecenter.service;

import com.get.common.result.Page;
import com.get.insurancecenter.dto.order.OrderListDto;
import com.get.insurancecenter.dto.order.UpdateOrderDto;
import com.get.insurancecenter.vo.order.OrderDetailVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/23
 * @Version 1.0
 * @apiNote:
 */
public interface InsuranceOrderService {

    /**
     * 查询保单列表
     * @param params
     * @param page
     * @return
     */
    List<OrderDetailVo> orderList(OrderListDto params, Page page);


    /**
     * 修改保单
     * @param orderDto
     */
    void updateOrder(UpdateOrderDto orderDto);

    /**
     * 订单详情
     * @param orderId
     * @return
     */
    OrderDetailVo getOrderInfo(Long orderId);
}

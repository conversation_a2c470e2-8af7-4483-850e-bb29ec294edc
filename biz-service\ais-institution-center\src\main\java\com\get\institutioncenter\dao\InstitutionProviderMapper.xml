<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionProviderMapper">
    <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionProvider" keyProperty="id"
            useGeneratedKeys="true">
        insert into m_institution_provider (id, fk_area_country_id, fk_area_state_id,
                                            fk_area_city_id, fk_institution_provider_type_id,
                                            fk_institution_group_id, fk_institution_channel_id,
                                            num, name, name_chn,
                                            zip_code, email, address,
                                            app_commission_deadline, is_active, public_level,
                                            id_gea, id_iae, gmt_create,
                                            gmt_create_user, gmt_modified, gmt_modified_user
        )
        values (#{id,jdbcType=BIGINT}, #{fkAreaCountryId,jdbcType=BIGINT}, #{fkAreaStateId,jdbcType=BIGINT},
                #{fkAreaCityId,jdbcType=BIGINT}, #{fkInstitutionProviderTypeId,jdbcType=BIGINT},
                #{fkInstitutionGroupId,jdbcType=BIGINT}, #{fkInstitutionChannelId,jdbcType=BIGINT},
                #{num,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{nameChn,jdbcType=VARCHAR},
                #{zipCode,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
                #{appCommissionDeadline,jdbcType=DATE}, #{isActive,jdbcType=BIT}, #{publicLevel,jdbcType=VARCHAR},
                #{idGea,jdbcType=VARCHAR}, #{idIae,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP},
                #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
               )
    </insert>
    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionProvider" keyProperty="id"
            useGeneratedKeys="true">
        insert into m_institution_provider
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkAreaCountryId != null">
                fk_area_country_id,
            </if>
            <if test="fkAreaStateId != null">
                fk_area_state_id,
            </if>
            <if test="fkAreaCityId != null">
                fk_area_city_id,
            </if>
            <if test="fkInstitutionProviderTypeId != null">
                fk_institution_provider_type_id,
            </if>
            <if test="fkInstitutionGroupId != null">
                fk_institution_group_id,
            </if>
            <if test="fkInstitutionChannelId != null">
                fk_institution_channel_id,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="nameChn != null">
                name_chn,
            </if>
            <if test="zipCode != null">
                zip_code,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="appCommissionDeadline != null">
                app_commission_deadline,
            </if>
            <if test="isActive != null">
                is_active,
            </if>
            <if test="publicLevel != null">
                public_level,
            </if>
            <if test="idGea != null">
                id_gea,
            </if>
            <if test="idIae != null">
                id_iae,
            </if>
            <if test="contractStatus != null">
                contract_status,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkAreaCountryId != null">
                #{fkAreaCountryId,jdbcType=BIGINT},
            </if>
            <if test="fkAreaStateId != null">
                #{fkAreaStateId,jdbcType=BIGINT},
            </if>
            <if test="fkAreaCityId != null">
                #{fkAreaCityId,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionProviderTypeId != null">
                #{fkInstitutionProviderTypeId,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionGroupId != null">
                #{fkInstitutionGroupId,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionChannelId != null">
                #{fkInstitutionChannelId,jdbcType=BIGINT},
            </if>
            <if test="num != null">
                #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameChn != null">
                #{nameChn,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="appCommissionDeadline != null">
                #{appCommissionDeadline,jdbcType=DATE},
            </if>
            <if test="isActive != null">
                #{isActive,jdbcType=BIT},
            </if>
            <if test="publicLevel != null">
                #{publicLevel,jdbcType=VARCHAR},
            </if>
            <if test="idGea != null">
                #{idGea,jdbcType=VARCHAR},
            </if>
            <if test="idIae != null">
                #{idIae,jdbcType=VARCHAR},
            </if>
            <if test="contractStatus != null">
                #{contractStatus,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>

        </trim>
    </insert>
    <select id="getInstitutionProviderIds" parameterType="java.lang.String" resultType="long">
        select
            ip.id
        from
            m_institution_provider ip
        where
                ip.name
                like
                #{institutionProviderName}
                or
                ip.name_chn
                like
                 #{institutionProviderName}
                or
                CONCAT(ip.`name`,'（',ip.name_chn,'）')
                like
                #{institutionProviderName}
        ORDER BY
        LENGTH(ip.name),LENGTH(ip.name_chn)
    </select>

    <select id="getInstitutionProvidersByName" resultType="com.get.institutioncenter.vo.InstitutionProviderVo">
         select
            *
        from
            m_institution_provider ip
        where
                ip.name
                like
                concat('%',#{keyword},'%')
                or
                ip.name_chn
                like
                 concat('%',#{keyword},'%')
                or
                CONCAT(ip.`name`,'（',ip.name_chn,'）')
                like
                concat('%',#{keyword},'%')
        ORDER BY
        LENGTH(ip.name),LENGTH(ip.name_chn)
    </select>

    <select id="getInstitutionProviderNameById" parameterType="java.lang.Long" resultType="string">
        select
            CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName
        from
            m_institution_provider ip
        where
            ip.id = #{id}
    </select>


    <select id="getInstitutionProviderTypeById" parameterType="java.lang.Long" resultType="string">
        select
            ipt.type_name
        from
            u_institution_provider_type ipt
                left join
                m_institution_provider ip
                on
                    ip.fk_institution_provider_type_id = ipt.id
        where
            ip.id = #{id}
    </select>

    <select id="getInstitutionProviders" resultType="com.get.institutioncenter.vo.InstitutionProviderVo">
        select distinct ip.* ,CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName from
        m_institution_provider ip LEFT JOIN r_institution_provider_company ipc on ip.id = ipc.fk_institution_provider_id
        LEFT JOIN r_institution_provider_area_country AS ipac ON ipac.fk_institution_provider_id = ip.id
        <where>
            <if test="institutionProviderDto.fkAreaCountryId!=null and institutionProviderDto.fkAreaCountryId !=''">
                and ip.fk_area_country_id = #{institutionProviderDto.fkAreaCountryId}
            </if>
            <if test="institutionProviderDto.areaCountryId!=null and institutionProviderDto.areaCountryId !=''">
                and ipac.fk_area_country_id = #{institutionProviderDto.areaCountryId}
            </if>
            <if test="institutionProviderDto.areaCountryIds!=null and institutionProviderDto.areaCountryIds.size()>0">
                and ipac.fk_area_country_id IN
                <foreach collection="institutionProviderDto.areaCountryIds" item="countryId" index="index" open="(" separator=","
                         close=")">
                    #{countryId}
                </foreach>
            </if>
            <if test="institutionProviderDto.fkCompanyId!=null and institutionProviderDto.fkCompanyId !=''">
                and ipc.fk_company_id = #{institutionProviderDto.fkCompanyId}
            </if>
            <if test="institutionProviderDto.fkCompanyIds!=null and institutionProviderDto.fkCompanyIds.size()>0">
                and ipc.fk_company_id IN
                <foreach collection="institutionProviderDto.fkCompanyIds" item="companyId" index="index" open="(" separator=","
                         close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="institutionProviderDto.keyWord!=null and institutionProviderDto.keyWord !=''">
                and (position(#{institutionProviderDto.keyWord,jdbcType=VARCHAR} in ip.name) or ip.NAME LIKE concat("%",#{institutionProviderDto.keyWord},"%") OR ip.name_chn LIKE concat("%",#{institutionProviderDto.keyWord},"%"))
            </if>
        </where>
    </select>

    <select id="getInstitutionProviderList" resultType="com.get.institutioncenter.vo.InstitutionProviderVo">
        select
        ip.id ,ipt.type_name as typeName,CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE
        CONCAT(`name`,'（',name_chn,'）') END name
        from
        m_institution_provider as ip
        left join
        u_institution_provider_type as ipt on ip.fk_institution_provider_type_id = ipt.id
        where
        ip.is_active = 1
        and
        ip.id
        in
        ( select DISTINCT(fk_institution_provider_id) from r_institution_provider_company where fk_company_id
        in
        <foreach item="item" index="index" collection="companyIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY
        fk_institution_provider_id
        )
        <if test="name != null and name != ''">
            AND ( ip.name LIKE concat('%',#{name},'%') or ip.name_chn LIKE concat('%',#{name},'%') )
        </if>

    </select>

    <select id="getInstitutionProviderListByCountry" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select DISTINCT mip.id,mip.name,mip.name_chn,concat(if(mip.is_active=0,'【无效】',''),
                                                            CASE
                                                                WHEN IFNULL(mip.name_chn,'') = ''
                                                                    THEN
                                                                    mip.name ELSE CONCAT(mip.name, '（', mip.name_chn, '）' )
                                                                END) fullName,mip.is_active status
        from u_area_country uac
                 left join r_institution_provider_area_country ripac on ripac.fk_area_country_id = uac.id
                 left join m_institution_provider mip on mip.id = ripac.fk_institution_provider_id
                 left join r_institution_provider_company ripc on ripc.fk_institution_provider_id = mip.id
        where ripc.fk_company_id = #{companyId} and uac.id = #{countryId}
    </select>

    <select id="getProviderList" resultType="com.get.institutioncenter.vo.InstitutionProviderVo">
        SELECT
        m.id,m.num,m.fk_area_country_id,m.fk_institution_provider_type_id,m.fk_institution_group_id,m.name,m.name_chn,r.id
        ipiId,
        r.is_active as isBindingActive
        FROM m_institution_provider m
        <if test="institutionProviderDto.fkCompanyIds!=null and institutionProviderDto.fkCompanyIds.size>0">
            INNER JOIN r_institution_provider_company rc ON rc.fk_institution_provider_id = m.id
        </if>
        left join r_institution_provider_institution r on m.id=r.fk_institution_provider_id
        WHERE
        ( 1=2
        <foreach collection="companyIds" item="companyId" index="index">
            or FIND_IN_SET(#{companyId}, r.fk_company_ids)
        </foreach>
        )
            <if test="institutionProviderDto.fkCompanyIds!=null and institutionProviderDto.fkCompanyIds.size>0">
                AND rc.fk_company_id in
                <foreach collection="institutionProviderDto.fkCompanyIds" open="(" close=")" item="cid" separator=",">
                    #{cid}
                </foreach>
            </if>
            <if test="institutionProviderDto.fkInstitutionId!=null and institutionProviderDto.fkInstitutionId !=''">
                and r.fk_institution_id=#{institutionProviderDto.fkInstitutionId}
            </if>
            <if test="institutionProviderDto.keyWord!=null and institutionProviderDto.keyWord !=''">
                and (position(#{institutionProviderDto.keyWord,jdbcType=VARCHAR} in m.name) or position(#{institutionProviderDto..keyWord,jdbcType=VARCHAR} in
                m.name_chn))
            </if>
        GROUP BY
        m.id
        ORDER BY
        r.is_active desc
    </select>


    <select id="getInstitutionProviderSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT p.id,
               concat(if(p.is_active = 0, '【无效】', ''),
                      CASE
                          WHEN IFNULL(p.name_chn, '') = ''
                              THEN
                              p.name
                          ELSE CONCAT(p.name, '（', p.name_chn, '）')
                          END) name,
               p.name_chn,
               p.is_active     status
        from m_institution_provider p
                 left join r_institution_provider_company c on p.id = c.fk_institution_provider_id
        where c.fk_company_id = #{companyId}
        ORDER BY p.is_active DESC,p.id ASC
    </select>
    <select id="getInstitutionProviderListByInstitution" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT p.id,
               ic.id as deputyId,
               CONCAT('【',
                      ic.name_chn,
                          '】',
                      IF(ipi.is_active = 0, '【无合作】', ''),
                      IF(p.is_active = 0, '【无效】', ''),
                      CASE
                          WHEN IFNULL(p.name_chn, '') = '' THEN
                              p.NAME
                          ELSE CONCAT(p.NAME, '（', p.name_chn, '）')
                          END
               ) fullName,
               p.name_chn,
               CASE
                   WHEN p.is_active = 0 THEN
                       0
                   WHEN ipi.is_active = 0 THEN
                       0
                   ELSE 1
                   END  status
        <!--p.is_active status-->
        FROM m_institution_provider p
                 INNER JOIN r_institution_provider_institution ipi ON ipi.fk_institution_provider_id = p.id
                 INNER JOIN r_institution_provider_institution_channel ipic on ipic.fk_institution_provider_id = p.id
                 INNER JOIN m_institution_channel ic on ipic.fk_institution_channel_id = ic.id
                 INNER JOIN r_institution_channel_company AS ricc ON ricc.fk_institution_channel_id = ic.id
                 INNER JOIN r_institution_provider_company AS ripc ON ripc.fk_institution_provider_id = p.id
        WHERE ipi.fk_institution_id = #{institutionId}
          AND FIND_IN_SET(#{studentCompanyId}, ipi.fk_company_ids)
          AND ricc.fk_company_id = #{studentCompanyId}
          AND ripc.fk_company_id = #{studentCompanyId}
        GROUP BY p.id,
                 ic.id
        ORDER BY status DESC,
                 p.id ASC
    </select>
    <select id="getCountrySelectByInstitutionProvider" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            ac.id,
            ac.num,
            ac.NAME,
            ac.name_chn,
            CASE WHEN IFNULL( ac.name_chn, '' )= '' THEN `name` ELSE CONCAT( `name`, '（', ac.name_chn, '）' ) END fullName
        FROM
            u_area_country AS ac
                INNER JOIN r_institution_provider_area_country AS c ON c.fk_area_country_id = ac.id
        WHERE
            c.fk_institution_provider_id = #{providerId}
        ORDER BY
            view_order DESC
    </select>

    <select id="getInstitutionProviderIdsByCompanyId" resultType="java.lang.Long">
        SELECT distinct fk_institution_provider_id FROM `r_institution_provider_company` where fk_company_id = #{companyId}
    </select>

    <select id="getInstitutionProviderIdsByChannelName" resultType="java.lang.Long">
        select mip.id from m_institution_provider mip left join m_institution_channel mic
        on mic.id = mip.fk_institution_channel_id
        where 1=1
        <if test="channelName!=null">
            and (mic.name LIKE CONCAT('%',#{channelName},'%')
            or mic.name_chn LIKE CONCAT('%',#{channelName},'%'))
        </if>
    </select>

    <select id="getInstitutionProviderIdsByChannelGroup" resultType="java.lang.Long">
        select mip.id from m_institution_provider mip
        where 1=1
        <if test="groupId!=null">
            and mip.fk_institution_group_id =#{groupId}
        </if>
        <if test="channelId!=null">
            and mip.fk_institution_channel_id =#{channelId}
        </if>
    </select>


    <select id="getInstitutionProviderIdsByCompanyIdAndName" resultType="java.lang.Long">
        SELECT
            ip.id
        FROM
        m_institution_provider ip
        LEFT JOIN r_institution_provider_company ripc ON ip.id = ripc.fk_institution_provider_id
        LEFT JOIN m_institution_group mp on mp.id = ip.fk_institution_group_id
        WHERE
        ripc.fk_company_id IN
        <foreach collection="companyIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        AND (
        REPLACE (ip. NAME, ' ', '') LIKE concat('%',#{institutionProviderName},'%')
        OR ip.name_chn LIKE concat('%',#{institutionProviderName},'%')
        OR REPLACE (mp.name, ' ', '') LIKE concat('%',#{institutionProviderName},'%')
        OR mp.name_chn LIKE concat('%',#{institutionProviderName},'%')
        )
        AND ip.is_active = 1
        GROUP BY
        ip.id
        ORDER BY mp.name,ip.name
        LIMIT 200
    </select>
    <select id="getInstitutionProviderIdsByCompanyIds" resultType="java.lang.Long">
        SELECT distinct fk_institution_provider_id FROM `r_institution_provider_company` where 1=1
        <if test="fkCompanyIds != null and fkCompanyIds.size() > 0">
            and fk_company_id in
            <foreach collection="fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
    </select>

    <select id="getInstitutionProviderByProviderName" resultType="com.get.institutioncenter.vo.InstitutionProviderInstitutionChannelVo">
        SELECT
        a.id,
        CONCAT(a.NAME,"【",c.short_name,"】") AS name
        FROM
            m_institution_provider a
            INNER JOIN r_institution_provider_company r on r.fk_institution_provider_id = a.id
            INNER JOIN ais_permission_center.m_company c on c.id = r.fk_company_id
        WHERE
            1 = 1
        <if test="queryVo.fkCompanyIds!=null and queryVo.fkCompanyIds.size>0">
            AND c.id IN
            <foreach collection="queryVo.fkCompanyIds" item="cid" open="(" close=")" separator=",">
                #{cid}
            </foreach>
        </if>
        <if test="queryVo.name!=null and queryVo.name != ''">
            and (a.name LIKE CONCAT('%',#{queryVo.name},'%')
            or a.name_chn LIKE CONCAT('%',#{queryVo.name},'%'))
        </if>
        group by a.id
        LIMIT 30
    </select>

    <select id="getInstitutionProviderByName" resultType="com.get.institutioncenter.vo.InstitutionProviderInstitutionChannelVo">
        SELECT
        a.id AS fkInstitutionProviderId,
        ic.id AS fkInstitutionChannelId,
        a.NAME AS providerName,
        a.name_chn AS providerNameChn,
        CONCAT('【',
        ic.name,
        IF( ic.name_chn is null or ic.name_chn='', '', CONCAT('（',ic.name_chn,'）')),
        '】',
        CASE
        WHEN IFNULL( a.name_chn, '' ) = '' THEN
        a.NAME ELSE CONCAT( a.NAME, '（', a.name_chn, '）' )
        END
        ) channelName,
        CONCAT( "【",
        GROUP_CONCAT( DISTINCT c.short_name ORDER BY c.id SEPARATOR '，' ),
        "】",ic.NAME) AS channelName,
        ic.name_chn AS channelNameChn
        FROM
        m_institution_provider a
        INNER JOIN r_institution_provider_institution_channel ipic on ipic.fk_institution_provider_id = a.id
        INNER JOIN m_institution_channel ic on ipic.fk_institution_channel_id = ic.id
        INNER JOIN r_institution_channel_company r on r.fk_institution_channel_id = ic.id
        INNER JOIN ais_permission_center.m_company c on c.id = r.fk_company_id
        WHERE
        1 = 1
        <if test="queryVo.fkCompanyIds!=null and queryVo.fkCompanyIds.size>0">
            AND c.id IN
            <foreach collection="queryVo.fkCompanyIds" item="cid" open="(" close=")" separator=",">
                #{cid}
            </foreach>
        </if>
        <if test="queryVo.channelProviderName!=null and queryVo.channelProviderName != ''">
            and (a.name LIKE CONCAT('%',#{queryVo.channelProviderName},'%')
            or a.name_chn LIKE CONCAT('%',#{queryVo.channelProviderName},'%')
            or ic.name LIKE CONCAT('%',#{queryVo.channelProviderName},'%')
            or ic.name_chn LIKE CONCAT('%',#{queryVo.channelProviderName},'%'))
        </if>
        group by a.id,ic.id
        LIMIT 30
    </select>
    <select id="getInstitutionSelectByCompanyId" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            p.id,
            p.`name`,
            p.name_chn,
            CONCAT(p.name_chn,'（',p.`name`,'）') as fullName
        FROM
            m_institution_provider p
        LEFT JOIN r_institution_provider_company c ON c.fk_institution_provider_id = p.id
        WHERE c.fk_company_id = #{companyId}
    </select>
    <select id="getInstitutionProviderByTargetName" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            p.id,
            p.`name`,
            p.name_chn,
            CONCAT(p.name_chn,'（',p.`name`,'）') as fullName
        FROM
            m_institution_provider p
        WHERE
            (REPLACE(LOWER(`name`),' ','') LIKE CONCAT('%',#{targetName},'%')
            or REPLACE(LOWER(name_chn),' ','') LIKE CONCAT('%',#{targetName},'%'))
        LIMIT 20
    </select>
    <select id="getInstitutionChannelProviderNamesByIds"
            resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            p.id,
            concat(
                '【',
                c.`name`,
                c.name_chn,
                '】',
                p.`name`,
                '（',
                p.name_chn,
                '）'
            ) AS fullName
        FROM
            m_institution_provider p
        INNER JOIN r_institution_provider_institution_channel r ON r.fk_institution_provider_id = p.id
        INNER JOIN m_institution_channel c ON r.fk_institution_channel_id = c.id
        WHERE
            p.id IN
            <foreach collection="providerIds" item="pid" open="(" separator="," close=")">
                #{pid}
            </foreach>
        GROUP BY p.id
    </select>
    <select id="getInstitutionProviderByInstitution" resultType="java.lang.Long">
        select ip.id
        from m_institution_provider ip
        left join r_institution_provider_institution ipi on ip.id = ipi.fk_institution_provider_id
        where ipi.is_active = 1
        <if test="institutionId!=null and institutionId !=''">
            and ipi.fk_institution_id = #{institutionId}
        </if>
    </select>
    
    <select id="getInstitutionByProvider" resultType="com.get.institutioncenter.entity.InstitutionProviderInstitution">
        SELECT * FROM r_institution_provider_institution
        WHERE is_active = 1
        <if test="fkCompanyIds != null and fkCompanyIds.size > 0">
            AND (
            <foreach collection="fkCompanyIds" item="companyId" separator=" OR ">
                FIND_IN_SET(#{companyId}, fk_company_ids)
            </foreach>
            )
        </if>
            <if test="fkInstitutionProviderIds!=null and fkInstitutionProviderIds.size>0">
                AND fk_institution_provider_id IN
                <foreach collection="fkInstitutionProviderIds" item="providerId" open="(" separator="," close=")">
                    #{providerId}
                </foreach>
            </if>

    </select>


    <select id="getInstitutionProvidersAndAreaCountryById"
            resultType="com.get.institutioncenter.vo.InstitutionProvidersAndAreaCountryVo">
        SELECT mip.id,
               mip.NAME,
               mip.name_chn,
               ripac.id                         AS institutionProviderAreaCountryId,
               ripac.fk_institution_provider_id AS institutionProviderId,
               ripac.fk_area_country_id         AS areaCountryId,
               ripac.gmt_create                 AS gmtCreate,
               ripac.gmt_create_user            AS gmtCreateUser
        FROM m_institution_provider mip
                 LEFT JOIN r_institution_provider_area_country ripac ON mip.id = ripac.fk_institution_provider_id
        WHERE mip.id = #{id};
    </select>
    <select id="findByContractStatus" resultType="com.get.institutioncenter.entity.InstitutionProvider">
        SELECT *
        FROM m_institution_provider AS institutionProvider
        <!-- 生效中/已过期 -->
        <where>
            <if test="status == 11 or status == 12">
                institutionProvider.id IN (
                SELECT p.fk_institution_provider_id
                FROM ais_pmp2_center.m_institution_provider_commission_plan AS p
                WHERE p.approval_status = 2
                AND EXISTS (
                SELECT 1
                FROM ais_pmp2_center.m_institution_provider_contract AS c
                WHERE p.fk_institution_provider_contract_id = c.id
                AND c.is_timeless = 0
                AND c.is_active = 1
                <!-- 动态切换时间条件 -->
                <choose>
                    <when test="status == 11">
                        AND CURRENT_DATE BETWEEN c.start_time AND c.end_time
                    </when>
                    <when test="status == 12">
                        AND CURRENT_DATE > c.end_time
                    </when>
                </choose>
                )
                group by p.fk_institution_provider_id
                )
            </if>

            <!-- 根据公司ID查询所在公司以及旗下公司对应的学校供应商 -->
            <if test="providerIdBYCompanyIds!=null and providerIdBYCompanyIds.size()>0">
                AND institutionProvider.id IN <foreach item="providerId" collection="providerIdBYCompanyIds"
                         open="(" separator="," close=")">
                #{providerId}
            </foreach>
            </if>

            <!-- 根据业务国家过滤 -->
            <if test="areaCountryProviderIds!=null and areaCountryProviderIds.size()>0">
                AND institutionProvider.id IN <foreach collection="areaCountryProviderIds" item="providerId"
                         open="(" separator="," close=")">
                #{providerId}
            </foreach>
            </if>

            <!-- 国家ID精确匹配 -->
            <if test="providerVo.fkAreaCountryId != null">
                AND institutionProvider.fk_area_country_id = #{providerVo.fkAreaCountryId}
            </if>

            <!-- 供应商类型 -->
            <if test="providerVo.fkInstitutionProviderTypeId != null">
                AND institutionProvider.fk_institution_provider_type_id = #{providerVo.fkInstitutionProviderTypeId}
            </if>

            <!-- 渠道过滤 -->
            <if test="institutionChannelIds!=null and  institutionChannelIds.size()>0">
                AND institutionProvider.id IN <foreach collection="institutionChannelIds" item="providerId"
                         open="(" separator="," close=")">
                #{providerId}
            </foreach>
            </if>

            <!-- 集团ID -->
            <if test="providerVo.fkInstitutionGroupId != null">
                AND institutionProvider.fk_institution_group_id = #{providerVo.fkInstitutionGroupId}
            </if>

            <!-- 是否激活 -->
            <if test="providerVo.isActive != null">
                AND institutionProvider.is_active = #{providerVo.isActive}
            </if>

            <!-- 合同状态处理 -->
            <if test="providerVo.contractStatus != null">
                <!-- 特殊处理：11 或 12 转换为 1 -->
                <choose>
                    <when test="providerVo.contractStatus == 11 or providerVo.contractStatus == 12">
                        AND institutionProvider.contract_status = 1
                    </when>
                    <otherwise>
                        AND institutionProvider.contract_status = #{providerVo.contractStatus}
                    </otherwise>
                </choose>
            </if>

            <!-- 公共等级模糊匹配 -->
            <if test="providerVo.publicLevel != null and providerVo.publicLevel != ''">
                AND institutionProvider.public_level LIKE CONCAT('%', #{providerVo.publicLevel}, '%')
            </if>

            <!-- 关键词搜索：名称/编号/中文名 -->
            <if test="providerVo.keyWord != null and providerVo.keyWord != ''">
                AND (
                institutionProvider.name LIKE CONCAT('%', #{providerVo.keyWord}, '%')
                OR institutionProvider.num LIKE CONCAT('%', #{providerVo.keyWord}, '%')
                OR institutionProvider.name_chn LIKE CONCAT('%', #{providerVo.keyWord}, '%')
                )
            </if>
        </where>

        <!-- 排序 -->
        ORDER BY id DESC
    </select>
    <select id="getContractExpiredByProviderId"
            resultType="com.get.institutioncenter.vo.InstitutionProviderContractReminderVo">
        SELECT
        mip.id,
        CASE
        WHEN IFNULL(mip.name_chn,'')='' THEN mip.name
        ELSE CONCAT(mip.name,'（',mip.name_chn,'）')
        END AS fullName,
        mip.name_chn AS nameChn,
        mip.name AS name,
        mipc.id AS contractId,
        mipc.contract_title AS contractName,
        mipc.start_time AS startTime,
        mipc.end_time AS endTime,
        (
        SELECT mipcpa.fk_staff_id
        FROM ais_pmp2_center.m_institution_provider_commission_plan_approval mipcpa
        WHERE mipcpa.id = (
        SELECT MAX(approval.id)
        FROM ais_pmp2_center.m_institution_provider_commission_plan_approval approval
        WHERE (
        approval.fk_institution_provider_commission_plan_id IN (
        SELECT plan.id
        FROM ais_pmp2_center.m_institution_provider_commission_plan plan
        WHERE plan.fk_institution_provider_contract_id = mipc.id
        )
        OR EXISTS (
        SELECT 1
        FROM ais_pmp2_center.m_institution_provider_commission_plan plan
        WHERE plan.fk_institution_provider_contract_id = mipc.id
        AND FIND_IN_SET(plan.id, approval.fk_institution_provider_commission_plan_ids) > 0
        )
        )
        AND approval.fk_staff_id IS NOT NULL
        )
        LIMIT 1
        ) AS fkStaffId,
        (
        SELECT ms.id
        FROM ais_permission_center.m_staff ms
        WHERE ms.login_id = mipc.gmt_create_user
        LIMIT 1
        ) AS gmtCreateUserId
        FROM
        ais_institution_center.m_institution_provider mip
        JOIN
        ais_pmp2_center.m_institution_provider_contract mipc ON mip.id = mipc.fk_institution_provider_id
        WHERE
         mipc.is_active = 1
        AND mip.contract_status = 1
        <if test="contractId != null">
            AND mipc.id = #{contractId}
        </if>
        ORDER BY
        mip.name, mipc.id
    </select>

    <select id="getActiveContractSchoolProviderIds" resultType="java.lang.Long">
        SELECT
            ip.id
        FROM
            m_institution_provider ip
                LEFT JOIN ais_pmp2_center.m_institution_provider_contract c ON c.fk_institution_provider_id = ip.id
                LEFT JOIN ais_pmp2_center.m_institution_provider_commission_plan p ON p.fk_institution_provider_contract_id = c.id
        WHERE
            c.is_active = 1
          AND (
                    c.is_timeless = 1
                OR (
                           c.start_time IS NOT NULL
                           AND c.end_time IS NOT NULL
                           AND CURRENT_DATE BETWEEN DATE(c.start_time )
                        AND DATE( c.end_time )
            )
	)
	AND p.approval_status = 2
        GROUP BY 	ip.id
    </select>

    <select id="getExpiredContractSchoolProviderIds" resultType="java.lang.Long">
        SELECT
            ip.id
        FROM
            m_institution_provider ip
                LEFT JOIN ais_pmp2_center.m_institution_provider_contract c ON c.fk_institution_provider_id = ip.id
                LEFT JOIN ais_pmp2_center.m_institution_provider_commission_plan p ON p.fk_institution_provider_contract_id = c.id
        WHERE
            c.is_active = 1
          AND (
                    c.is_timeless = 1
                OR (
                           c.start_time IS NOT NULL
                           AND c.end_time IS NOT NULL
                           AND CURRENT_DATE > DATE( c.end_time )
            )
	)
	AND p.approval_status = 2
        GROUP BY 	ip.id
    </select>


</mapper>
package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.vo.AreaCountryHtiVo;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.institutioncenter.entity.AreaCountry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2020/7/17 11:23
 * @verison: 1.0
 * @description: 区域管理-国家配置mapper
 */
@Mapper
public interface AreaCountryMapper extends BaseMapper<AreaCountry> {




    /**
     * feign调用 根据国家编号查找国家名称
     *
     * @param key
     * @return
     */
    String getCountryNameByKey(String key);

    /**
     * feign调用 根据国家编号查找国家名称
     *
     * @param key
     * @return
     */
    String getCountryNameEnByKey(String key);


    /**
     * 根据国家id查找国家名称
     *
     * @param id
     * @return
     */
    String getCountryNameById(Long id);

    /**
     * 当前登录人国家下拉框
     *
     * @return
     */
    List<BaseSelectEntity> getCountryList(@Param("countryIds") List<Long> countryIds);

    /**
     * 所有国家下拉框
     *
     * @return
     */
    List<AreaCountryVo> getAllCountryList();

    /**
     * feign调用 根据国家编号查找国家id
     *
     * @param key
     * @return
     */
    Long getCountryIdByKey(String key);

    /**
     * 获取最大排序值
     *
     * @return
     */
    Integer getMaxViewOrder();

    /**
     * 根据国家id查找国家全称
     *
     * @param id
     * @return
     */
    String getCountryFullNameById(Long id);

    /**
     * 根据国家id查找国家中文名称
     *
     * @param id
     * @return
     */
    String getCountryNameChnById(Long id);


    List<Long> getAreaCountryIds(@Param("columnName") String columnName);

    /**
     * 根据国家id查找国家
     *
     * @param id
     * @return
     */
    AreaCountry getAreaCountryById(@Param("id") Long id);

    String getCurrencyNumByName(String name);

    /**
     * feign调用 查询全部 公开首页国家id
     *
     * @Date 17:03 2021/11/16
     * <AUTHOR>
     */
    List<Map<String, String>> selectPublicCountryHomeNums(@Param("publicLevel") Integer publicLevel);

    /**
     * 根据国家id获取国家名和国家编号
     *
     * @Date 12:13 2022/1/18
     * <AUTHOR>
     */
    String getCountryNameAndNumById(@Param("id") Long id);

    /**
     * 根据国家keys获取国家列表
     *
     * @param keys
     * @return
     */
    List<AreaCountryVo> getCountryByKey(@Param("keys") List<String> keys);
    /**
     * 根据国家名称模糊查询国家ID
     * <AUTHOR>
     * @DateTime 2022/11/24 14:45
     */
    List<Long> getCountryByName(@Param("name") String name);

    List<AreaCountryVo> getAreaCode();

    List<AreaCountryVo> getCommonAreaCodes();


    List<BaseSelectEntity> getAreaCountryListByKeyWord(@Param("keyword") String keyword,@Param("countryIds") List<Long> countryIds);

    /**
     * 获取对应公司下有申请计划的 业务国家下拉框数据
     *
     * @Date 12:44 2023/1/5
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsOfferItemAreaCountryList(@Param("companyId") Long companyId,
                                                             @Param("countryIds") List<Long> countryIds);

    /**
     * 获取对应公司下有申请计划的代理所在的 国家下拉框数据
     *
     * @Date 15:23 2023/1/5
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsAgentOfferItemAreaCountryList(@Param("companyId") Long companyId,
                                                                  @Param("countryIds") List<Long> countryIds);

    /**
     * 获取对应公司下的代理所在的 国家下拉框数据
     *
     * @Date 10:42 2023/3/15
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsAgentAreaCountryList(@Param("companyId") Long companyId, @Param("countryIds") List<Long> countryIds);

    /**
     * 获取新闻国家下拉
     * <AUTHOR>
     * @DateTime 2023/7/11 14:12
     */
    List<BaseSelectEntity> getNewsAreaCountryList();

    /**
     * 国家下拉 公开对象
     * @param publicLevel
     * @return
     */
    List<AreaCountryVo> getCountryByPublicLevel(@Param("publicLevel")Integer publicLevel);

    /**
     * HTI国家下拉框
     *
     * @Date 11:22 2023/12/14
     * <AUTHOR>
     */
    List<AreaCountryHtiVo> getAreaCountryListHti();
}
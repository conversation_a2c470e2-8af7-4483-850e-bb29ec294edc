<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.TravelClaimFormMapper">

    <select id="getExistParentId" resultType="com.get.financecenter.vo.TravelClaimFormVo">
        select * from m_travel_claim_form where fk_travel_claim_form_id_revoke=#{id}
    </select>

    <select id="getTravelClaimFormTotalAmount" resultType="java.math.BigDecimal">
        select IFNULL(sum(amount), 0) from m_travel_claim_form_item where fk_travel_claim_form_id = #{id}
    </select>

    <select id="getTravelClaimFormByTypeId" resultType="java.lang.Integer">
        SELECT COUNT(0) from m_travel_claim_form_item where fk_travel_claim_fee_type_id = #{travelClaimFeeTypeId}
    </select>
</mapper>
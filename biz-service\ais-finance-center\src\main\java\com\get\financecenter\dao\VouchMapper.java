package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.dto.VouchDto;
import com.get.financecenter.entity.Vouch;
import com.get.financecenter.vo.VouchVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface VouchMapper extends BaseMapper<Vouch> {

    List<VouchVo> getVouchs(IPage<Vouch> pages, @Param("vouchDto") VouchDto vouchDto);

}
package com.get.institutioncenter.service;


import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.ContactPersonDto;
import com.get.institutioncenter.dto.ContractDto;
import com.get.institutioncenter.dto.InstitutionChannelCompanyQueryDto;
import com.get.institutioncenter.dto.InstitutionChannelCompanySearchDto;
import com.get.institutioncenter.dto.InstitutionDto;
import com.get.institutioncenter.dto.InstitutionProviderCompanyDto;
import com.get.institutioncenter.dto.InstitutionProviderDto;
import com.get.institutioncenter.dto.InstitutionProviderInstitutionDto;
import com.get.institutioncenter.dto.NewsDto;
import com.get.institutioncenter.dto.ProviderInstitutionCompanyUpdateDto;
import com.get.institutioncenter.dto.ProviderInstitutionRelationDto;
import com.get.institutioncenter.dto.query.ContractQueryDto;
import com.get.institutioncenter.dto.query.InstitutionProviderQueryDto;
import com.get.institutioncenter.dto.query.NewsQueryDto;
import com.get.institutioncenter.entity.InstitutionProvider;
import com.get.institutioncenter.vo.ContactPersonVo;
import com.get.institutioncenter.vo.ContractVo;
import com.get.institutioncenter.vo.InstitutionProviderContractReminderVo;
import com.get.institutioncenter.vo.InstitutionProviderInstitutionChannelVo;
import com.get.institutioncenter.vo.InstitutionProviderInstitutionVo;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.institutioncenter.vo.InstitutionProvidersAndAreaCountryVo;
import com.get.institutioncenter.vo.InstitutionVo;
import com.get.institutioncenter.vo.NewsVo;
import com.get.institutioncenter.vo.ProviderInstitutionRelationVo;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;

/**
 * @author: Sea
 * @create: 2020/7/14 12:06
 * @verison: 1.0
 * @description:
 */
public interface IInstitutionProviderService extends BaseService<InstitutionProvider> {
    /**
     * @return java.lang.Long
     * @Description: 新增提供商
     * @Param [providerVo]
     * <AUTHOR>
     */
    Long addInstitutionProvider(InstitutionProviderDto providerVo);


    /**
     * 更新学校提供商合同状态
     * @param id
     * @param status
     * @return
     */
    Integer updateContractStatus(Long id,Integer status);


    /**
     * @return void
     * @Description: 删除提供商
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.institutioncenter.vo.InstitutionProviderVo
     * @Description: 修改提供商
     * @Param [providerVo]
     * <AUTHOR>
     */
    InstitutionProviderVo updateInstitutionProvider(InstitutionProviderDto providerVo);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 获取提供商列表数据
     * @Param [providerVo, page]
     * <AUTHOR>
     */
    List<InstitutionProviderVo> getProviderList(InstitutionProviderQueryDto providerVo, Page page);

    /**
     * @return com.get.institutioncenter.vo.InstitutionProviderVo
     * @Description: 提供商详情
     * @Param [id]
     * <AUTHOR>
     */
    InstitutionProviderVo findInstitutionProviderById(Long id);

    /**
     * 获取学校提供商名称
     *
     * @param providerId
     * @param channelId
     * @return
     */
    ResponseBo<String> getInfo(Long providerId, Long channelId);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 获取提供商列表数据 （学校详情获取）
     * @Param [institutionId, keyWord]
     * <AUTHOR>
     */
    List<InstitutionProviderVo> getInstitutionProviderList(InstitutionProviderDto providerVo, Page page);


    /**
     * @return java.lang.Long
     * @Description: 新增代理
     * @Param [contactPersonDto]
     * <AUTHOR>
     */
    Long addContactPerson(ContactPersonDto contactPersonDto);


    /**
     * @return java.util.List<com.get.institutioncenter.vo.ContactPersonDto>
     * @Description: 获取代理联系人
     * @Param [contactPersonDto, page]
     * <AUTHOR>
     */
    List<ContactPersonVo> getInstitutionContactPersonDtos(ContactPersonDto contactPersonDto, Page page);

    /**
     * @return java.lang.Long
     * @Description: 添加新闻
     * @Param [newsDto]
     * <AUTHOR>
     */
    Long addNews(NewsDto newsDto);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.NewsVo>
     * @Description: 查询新闻
     * @Param [data, page]
     * <AUTHOR>
     */
    List<NewsVo> getNewsData(NewsQueryDto data, Page page);

    /**
     * 编辑提供商和学校的关系
     *
     * @param providerInstitutionVos
     */
    void editProviderInstitutionRelation(List<InstitutionProviderInstitutionDto> providerInstitutionVos);


    /**
     * 编辑提供商和公司的关系
     * 修改/新增共用接口
     *
     * @param providerCompanyVos
     */
    void editProviderCompanyRelation(List<InstitutionProviderCompanyDto> providerCompanyVos);


    /**
     * 提供商和公司的关系（数据回显）
     *
     * @param providerId
     * @return
     * @
     */
    List<CompanyTreeVo> getProviderCompanyRelation(Long providerId);


    /**
     * @return com.get.institutioncenter.vo.InstitutionVo
     * @Description: 提供商和学校的关系（数据回显）
     * @Param [data, page]
     * <AUTHOR>
     */
    List<ProviderInstitutionRelationVo> getProviderInstitutionRelation(ProviderInstitutionRelationDto data, Page page);

    /**
     * 回显提供商关系
     *
     * @param institutionProviderDto
     * @return
     */
    List<InstitutionProviderVo> getInstitutionProviders(InstitutionProviderDto institutionProviderDto, Page page);


    /**
     * feign调用 根据输入的学校提供商id 查询对应的学校提供商名称
     *
     * @param id
     * @return
     * @
     */
    String getInstitutionProviderNameById(Long id);

    /**
     * @return java.util.Map<java.lang.Long, java.lang.String>
     * @Description :fegin调用 根据学校提供商ids 查询名称map
     * @Param [ids]
     * <AUTHOR>
     */
    Map<Long, String> getInstitutionProviderNamesByIds(Set<Long> ids);

    /**
     * feign调用 根据学校提供商id查找学校提供商类型
     *
     * @param id
     * @return
     */
    String getInstitutionProviderTypeById(Long id);


    /**
     * feign调用 根据输入的学校提供商名称 模糊查询对应的学校提供商id
     *
     * @param institutionProviderName
     * @return
     * @
     */
    List<Long> getInstitutionProviderIdsByName(String institutionProviderName);


    /**
     * 根据提供商名称关键 模糊查询提供商
     *
     * <AUTHOR>
     * @DateTime 2023/12/19 17:35
     */
    List<InstitutionProviderVo> getInstitutionProvidersByName(String keyword);

    /**
     * 获取学校下面的提供商列表
     *
     * @param institutionId
     * @param
     * @return
     */
    Set<Long> getInstitutionProviderByInstitution(Long institutionId);


    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 学校提供商下拉框数据
     * @Param []
     * <AUTHOR>
     */
    List<InstitutionProviderVo> getInstitutionProviderList(Long companyId, String name);



    List<InstitutionProviderVo> getInstitutionProviders(Long companyId, String name);

    /**
     * 获取所有提供商下拉
     *
     * @return
     */
    List<BaseSelectEntity> getAllInstitutionSelection();

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 学校提供商下拉框数据（feign）
     * @Param [companyId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getInstitutionProviderSelect(Long companyId);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 业务国家学校提供商下拉框数据
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getInstitutionProviderListByCountry(Long countryId);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.ContractVo>
     * @Description: 获取提供商合同
     * @Param [data, page]
     * <AUTHOR>
     */
    List<ContractVo> getContract(ContractQueryDto data, Page page);

    /**
     * @return java.lang.Long
     * @Description: 新增提供商合同
     * @Param [contractDto]
     * <AUTHOR>
     */
    Long addContract(ContractDto contractDto);


    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionVo>
     * @Description: 获取提供商详情中的学校
     * @Param [data, page]
     * <AUTHOR>
     */
    List<InstitutionVo> getInstitution(InstitutionDto data, Page page);


    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionVo>
     * @Description: 根据国家id查询提供商
     * @Param [countryIds]
     * <AUTHOR>
     */
    List<InstitutionProviderVo> getProviderList(List<Long> countryIds);


    /**
     * @return java.lang.String
     * @Description: 根据id查询名称
     * @Param [providerId]
     * <AUTHOR>
     */
    String getNameByProviderId(Long providerId);


    /**
     * 根据提供商ids获取名称map
     *
     * @param providerIds
     * @return
     */
    Map<Long, String> getNameByProviderIds(Set<Long> providerIds);

    /**
     * 根据学校获取绑定的学校提供商下拉框数据
     *
     * @Date 16:06 2021/8/18
     * <AUTHOR>
     */
    List<BaseSelectEntity> getInstitutionProviderListByInstitution(Long institutionId, Long studentCompanyId);

    /**
     * 根据提供商id获取业务国家下拉框数据
     *
     * @Date 10:31 2021/9/9
     * <AUTHOR>
     */
    List<BaseSelectEntity> getCountrySelectByInstitutionProvider(Long providerId);

    /**
     * feign调用 获取所有的渠道Map
     *
     * @Date 16:01 2021/11/23
     * <AUTHOR>
     */
    Map<Long, String> getInstitutionProviderChannel();

    /**
     * feign调用 获取渠道名By id
     *
     * @param
     * @return
     */
    String getInstitutionProviderChannelById(Long id);

    Map<Long, String> getInstitutionProviderChannelByIds(Set<Long> ids);

    /**
     * 获取提供商下拉名称
     *
     * @param ids
     * @return
     */
    Map<Long, String> getInstitutionProviderSelectNamesByIds(Set<Long> ids);

    /**
     * 根据公司id获取提供商ids
     *
     * @param companyId
     * @return
     */
    List<Long> getInstitutionProviderIdsByCompanyId(Long companyId);

    /**
     * 根据公司id获取提供商ids
     *
     * @param fkCompanyIds
     * @return
     */
    List<Long> getInstitutionProviderIdsByCompanyIds(List<Long> fkCompanyIds);

    /**
     * 根据渠道名称获取提供商ids
     *
     * @param channelName
     * @return
     */
    List<Long> getInstitutionProviderIdsByChannelName(String channelName);

    /**
     * 根据集团名称获取提供商ids
     *
     * @param channelId,groupId
     * @return
     */
    List<Long> getInstitutionProviderIdsByChannelGroup(Long groupId, Long channelId);

    /**
     * 根据公司和名称搜索提供商ids
     *
     * @param companyIds
     * @param institutionProviderName
     * @return
     */
    List<Long> getInstitutionProviderIdsByCompanyIdAndName(List<Long> companyIds, String institutionProviderName);

    /**
     * 根据提供商名称获取学校提供商信息和渠道信息
     *
     * @param queryVo
     * @return
     */
    List<InstitutionProviderInstitutionChannelVo> getInstitutionProviderByName(InstitutionChannelCompanyQueryDto queryVo);

    /**
     * 解绑合作关系和绑定合作关系
     *
     * @param institutionId
     * @param institutionProviderId
     * @param isBindingActive
     */
    void updateBindingActive(Long institutionId, Long institutionProviderId, Integer isBindingActive);

    /**
     * 获取活动绑定的提供商
     *
     * @param providerIds
     * @return
     */
    List<InstitutionProviderVo> getEventRegistrationProviderByIds(List<Long> providerIds);

    /**
     * 绑定关系回显
     *
     * @param institutionProviderDto
     * @param page
     * @return
     */
    List<InstitutionProviderVo> getEventRegistrationProviders(InstitutionProviderDto institutionProviderDto, Page page);

    /**
     * 根据提供商名称获取学校提供商信息信息(百度式搜索)
     *
     * @param queryVo
     * @return
     */
    List<InstitutionProviderInstitutionChannelVo> getInstitutionProviderByProviderName(InstitutionChannelCompanySearchDto queryVo);

    /**
     * 获取公司下的提供商列表
     *
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getInstitutionSelectByCompanyId(Long companyId);

    /**
     * 获取提供商下拉根据对象值
     *
     * @param targetName
     * @return
     */
    List<BaseSelectEntity> getInstitutionProviderByTargetName(String targetName);

    /**
     * 获取提供商和渠道拼接的名称
     *
     * @param providerIds
     * @return
     */
    Map<Long, String> getInstitutionChannelProviderNamesByIds(Set<Long> providerIds);

    /**
     * 根据学校提供商id获取提供商
     *
     * @Date 15:00 2023/9/26
     * <AUTHOR>
     */
    InstitutionProviderVo getInstitutionProviderById(Long fkInstitutionProviderId);

    /**
     * 提供商-学校安全配置
     *
     * @Date 12:25 2023/12/4
     * <AUTHOR>
     */
    void updateProviderInstitutionCompanyRelation(ProviderInstitutionCompanyUpdateDto providerInstitutionCompanyUpdateDto);

    /**
     * 根据提供商ids 获取提供商对象Map
     *
     * @Date 18:01 2023/12/18
     * <AUTHOR>
     */
    Map<Long, InstitutionProviderVo> getInstitutionProviderMapByIds(Set<Long> institutionProviderIds);

    /**
     * 根据公司id和提供商ids 获取学校
     *
     * @param fkCompanyId              公司id
     * @param fkInstitutionProviderIds 提供商ids
     * @return
     */
    List<InstitutionProviderInstitutionVo> getInstitutionByProvider(Set<Long> fkCompanyId, Set<Long> fkInstitutionProviderIds);

    List<InstitutionVo> getMpsInstitutionProviderList(Long fkProviderId);

    ResponseBo batchUpdateProvidersToRenewing(Set<Long> institutionProviderIds);


    /**
     * @param id
     * @return
     * 获取业务提供商及其业务国家信息
     */
    List<InstitutionProvidersAndAreaCountryVo> getInstitutionProvidersAndAreaCountryById(Long id);

    /**
     * 导出学校提供商下的学校信息
     * @param institutionDto
     * @param response
     */
    void exportInstitution(InstitutionDto institutionDto,Page page, HttpServletResponse response);

    /**
     * 根据合同id获取学校提供和学校提供商合同信息
     * @param contractId
     * @return
     */
    InstitutionProviderContractReminderVo getContractExpiredByProviderId(Long contractId);

    Boolean checkAreaCountryId(InstitutionProviderDto providerVo);
}

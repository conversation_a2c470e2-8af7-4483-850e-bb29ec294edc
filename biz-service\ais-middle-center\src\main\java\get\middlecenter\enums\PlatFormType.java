package get.middlecenter.enums;

import com.get.core.mybatis.base.BaseSelectEntity;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum PlatFormType {
    AIS(1L, "AIS"),
    PARTNER(2L, "华通伙伴")
//    ,AOXIAOBAO(3, "澳小宝")
    ;

    private final Long id;
    private final String name;

    PlatFormType(Long id, String name) {
        this.id = id;
        this.name = name;
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static String getNameById(Long id) {
        for (PlatFormType itemType : PlatFormType.values()) {
            if (itemType.getId().equals(id)) {
                return itemType.getName();
            }
        }
        return null;
    }

    /**
     * 获取所有枚举值作为选择列表
     */
    public static List<BaseSelectEntity> asSelectList() {
        return Arrays.stream(values())
                .map(type ->{
                    BaseSelectEntity selectEntity = new BaseSelectEntity();
                    selectEntity.setId(type.getId());
                    selectEntity.setName(type.getName());
                    selectEntity.setNameChn(type.getName());
                    selectEntity.setFullName(type.getName());
                    return selectEntity;
                })
                .collect(Collectors.toList());
    }


}

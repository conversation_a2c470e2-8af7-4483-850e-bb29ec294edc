<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.AreaRegionMapper">
    <insert id="insert" parameterType="com.get.institutioncenter.entity.AreaRegion">
        insert into u_area_region (id, fk_area_country_id, num,
                                   name, name_chn, remark,
                                   view_order, gmt_create, gmt_create_user,
                                   gmt_modified, gmt_modified_user)
        values (#{id,jdbcType=BIGINT}, #{fkAreaCountryId,jdbcType=BIGINT}, #{num,jdbcType=VARCHAR},
                #{name,jdbcType=VARCHAR}, #{nameChn,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
                #{viewOrder,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR},
                #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.AreaRegion" keyProperty="id" useGeneratedKeys="true">
        insert into u_area_region
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkAreaCountryId != null">
                fk_area_country_id,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="nameChn != null">
                name_chn,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="viewOrder != null">
                view_order,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkAreaCountryId != null">
                #{fkAreaCountryId,jdbcType=BIGINT},
            </if>
            <if test="num != null">
                #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameChn != null">
                #{nameChn,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="viewOrder != null">
                #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.get.institutioncenter.entity.AreaRegion">
        update u_area_region
        <set>
            <if test="fkAreaCountryId != null">
                fk_area_country_id = #{fkAreaCountryId,jdbcType=BIGINT},
            </if>
            <if test="num != null">
                num = #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameChn != null">
                name_chn = #{nameChn,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="viewOrder != null">
                view_order = #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.get.institutioncenter.entity.AreaRegion">
        update u_area_region
        set fk_area_country_id = #{fkAreaCountryId,jdbcType=BIGINT},
            num                = #{num,jdbcType=VARCHAR},
            name               = #{name,jdbcType=VARCHAR},
            name_chn           = #{nameChn,jdbcType=VARCHAR},
            remark             = #{remark,jdbcType=VARCHAR},
            view_order         = #{viewOrder,jdbcType=INTEGER},
            gmt_create         = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_create_user    = #{gmtCreateUser,jdbcType=VARCHAR},
            gmt_modified       = #{gmtModified,jdbcType=TIMESTAMP},
            gmt_modified_user  = #{gmtModifiedUser,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getAreaRegionDtoByIds" resultType="com.get.institutioncenter.vo.AreaRegionVo">
        SELECT
        uar.id AS id,
        uar.fk_area_country_id AS fkAreaCountryId,
        uar.num AS num,
        uar.`name` AS name,
        uar.name_chn AS nameChn,
        uar.remark AS remark,
        uar.view_order AS viewOrder,
        uar.gmt_create AS gmtCreate,
        uar.gmt_create_user AS gmtCreateUser,
        uar.gmt_modified AS gmtModified,
        uar.gmt_modified_user AS gmtModifiedUser,
        concat( uac.NAME, '【', uac.name_chn, '】', '-', uar.NAME, '（', uar.name_chn, '）' ) AS fullName
        FROM
        u_area_region uar
        LEFT JOIN u_area_country AS uac ON uar.fk_area_country_id = uac.id
        where uar.id in
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getAreaRegionSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT uar.id,concat(uac.name,'【',uac.name_chn,'】','-',uar.name,'（',uar.name_chn,'）') AS name FROM u_area_region AS uar LEFT JOIN u_area_country AS uac
                                                                                                                                          ON uar.fk_area_country_id = uac.id
        where 1=1
        <if test="fkCompanyId !=null and fkCompanyId !=''">
            and uar.fk_company_id = #{fkCompanyId}
        </if>
        AND uar.fk_company_id in
        <foreach collection="companyIds" item="fkCompanyId" open="(" separator="," close=")">
            #{fkCompanyId}
        </foreach>
        ORDER BY uar.view_order DESC
    </select>

    <select id="getAreaRegionSelectByCompanyIds" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT uar.id,concat(uac.name,'【',uac.name_chn,'】','-',uar.name,'（',uar.name_chn,'）') AS name FROM u_area_region AS uar LEFT JOIN u_area_country AS uac
        ON uar.fk_area_country_id = uac.id
       <where>
           <if test="fkCompanyIds !=null and fkCompanyIds.size()>0">
               and uar.fk_company_id IN
               <foreach collection="fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
                   #{fkCompanyId}
               </foreach>
           </if>
       </where>
        ORDER BY uar.view_order DESC
    </select>
    <select id="getByCountryId" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            u.id,
            u. NAME,
            u.name_chn,
            CASE
            WHEN m.short_name IS NOT NULL
            THEN
                CONCAT(
                    '【',
                    IFNULL(m.short_name, ''),
                    '】',
                    u. NAME,
                    '（',
                    u.name_chn,
                    '）'
                )
            ELSE
                CONCAT(
                    u. NAME,
                    '（',
                    u.name_chn,
                    '）'
                )
            END AS full_name
        FROM
            u_area_region u
        LEFT JOIN ais_permission_center.m_company m ON m.id = u.fk_company_id
        WHERE
            u.fk_area_country_id = #{countryId}
            AND m.id IN
        <foreach collection="fkCompanyIds" item="fkCompanyId" open="(" separator="," close=")">
            #{fkCompanyId}
        </foreach>
        order by m.view_order desc, u.view_order desc


    </select>

    <select id="getRegionMapByStateIds" resultType="com.get.institutioncenter.vo.AreaRegionVo">
        SELECT uar.*, rars.fk_area_state_id AS fkAreaStateId FROM u_area_region AS uar
        INNER JOIN r_area_region_state AS rars ON rars.fk_area_region_id = uar.id
        where rars.fk_area_state_id IN
        <foreach collection="stateIds" item="stateId" open="(" separator="," close=")">
            #{stateId}
        </foreach>
    </select>

</mapper>
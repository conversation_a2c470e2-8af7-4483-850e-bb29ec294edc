package get.middlecenter.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.aismiddle.dto.ReleaseInfoAndItemDto;
import com.get.aismiddle.dto.ReleaseInfoSearchDto;
import com.get.aismiddle.entity.MReleaseInfo;
import com.get.aismiddle.vo.ReleaseInfoAndItemVo;
import com.get.aismiddle.vo.ReleaseInfoItemVo;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import get.middlecenter.dao.ReleaseInfoMapper;
import get.middlecenter.enums.PlatFormType;
import get.middlecenter.service.MReleaseInfoItemService;
import get.middlecenter.service.MReleaseInfoService;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 发版信息项服务实现类
 */
@Service("middleMReleaseInfoService")
public class MReleaseInfoServiceImpl extends ServiceImpl<ReleaseInfoMapper, MReleaseInfo> implements MReleaseInfoService {
    @Resource
    private ReleaseInfoMapper releaseInfoMapper;

    @Resource
    private UtilService utilService;

    @Resource
    private MReleaseInfoItemService mReleaseInfoItemService;


    /**
     * 获取平台类型下拉框
     * @return
     */
    @Override
    public List<BaseSelectEntity> getPlatformTypeDropDown() {
        List<BaseSelectEntity> typeMap = PlatFormType.asSelectList();
        return typeMap;
    }

    @Override
    public List<ReleaseInfoAndItemVo> getReleaseInfoAndItem(ReleaseInfoSearchDto releaseInfoSearchDto, Page page) {
        // 查询数据
        IPage<MReleaseInfo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ReleaseInfoAndItemVo> mReleaseInfoDatas = releaseInfoMapper.getReleaseInfo(pages,releaseInfoSearchDto);
        if (GeneralTool.isNotEmpty(mReleaseInfoDatas)){
            return mReleaseInfoDatas;
        }
        return Collections.emptyList();
    }

    /**
     * 添加发版信息项和子项
     * @param releaseInfoAndItemDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        verifyParameters(releaseInfoAndItemDto);
        MReleaseInfo mReleaseInfo = BeanCopyUtils.objClone(releaseInfoAndItemDto, MReleaseInfo::new);
        utilService.setCreateInfo(mReleaseInfo);
        releaseInfoMapper.insert(mReleaseInfo);
        if (GeneralTool.isNotEmpty(releaseInfoAndItemDto.getReleaseInfoItemDtos())){
            releaseInfoAndItemDto.getReleaseInfoItemDtos().forEach(releaseInfoItemDto -> {
                releaseInfoItemDto.setFkReleaseInfoId(mReleaseInfo.getId());
                mReleaseInfoItemService.insert(releaseInfoItemDto);
            });
        }
    }

    private static void verifyParameters(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        if (GeneralTool.isEmpty(releaseInfoAndItemDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(releaseInfoAndItemDto.getFkPlatformId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("platform_id_cannot_be_empty"));
        }
        //如何平台类型为伙伴，code则为必填
        if (releaseInfoAndItemDto.getFkPlatformId().equals(PlatFormType.PARTNER.getId())){
            if (GeneralTool.isEmpty(releaseInfoAndItemDto.getFkPlatformCode())){
                throw new GetServiceException(LocaleMessageUtils.getMessage("platform_code_cannot_be_empty"));
            }
        }
        if (GeneralTool.isEmpty(releaseInfoAndItemDto.getTitle())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("title_cannot_be_empty"));
        }
        if (releaseInfoAndItemDto.getTitle().length() > 100) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("character_limit_exceeded")+"100" + " (" + releaseInfoAndItemDto.getTitle() + ")");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteReleaseInfoAndItem(Long id) {
        if (GeneralTool.isEmpty( id )){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        MReleaseInfo mReleaseInfo = releaseInfoMapper.selectById(id);
        if (GeneralTool.isEmpty(mReleaseInfo)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //查询对应的子项数据
        List<ReleaseInfoItemVo> releaseInfoItemByFkReleaseInfoId = mReleaseInfoItemService.getReleaseInfoItemByFkReleaseInfoId(id);
        if (GeneralTool.isNotEmpty(releaseInfoItemByFkReleaseInfoId)){
            List<Long> mReleaseInfoItemIds = releaseInfoItemByFkReleaseInfoId.stream().map(ReleaseInfoItemVo::getId).collect(Collectors.toList());
            //删除对应的子项数据
            mReleaseInfoItemService.deleteBatch(mReleaseInfoItemIds);
        }

        releaseInfoMapper.deleteById(id);

    }

    /**
     * 根据id获取详细信息
     * @param id
     * @return
     */
    @Override
    public ReleaseInfoAndItemVo getDetailedInformationById(Long id) {
        if (GeneralTool.isEmpty( id )){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        MReleaseInfo mReleaseInfo = releaseInfoMapper.selectById(id);
        if (GeneralTool.isEmpty(mReleaseInfo)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ReleaseInfoAndItemVo releaseInfoAndItemVo = BeanCopyUtils.objClone(mReleaseInfo, ReleaseInfoAndItemVo::new);
        List<ReleaseInfoItemVo> releaseInfoItemByFkReleaseInfoId = mReleaseInfoItemService.getReleaseInfoItemByFkReleaseInfoId(id);
        releaseInfoAndItemVo.setReleaseInfoItemVos(releaseInfoItemByFkReleaseInfoId);
        return releaseInfoAndItemVo;
    }

    /**
     * 编辑发版信息项和子项
     * @param releaseInfoAndItemDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        if (GeneralTool.isEmpty(releaseInfoAndItemDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(releaseInfoAndItemDto.getId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        MReleaseInfo mReleaseInfo = BeanCopyUtils.objClone(releaseInfoAndItemDto, MReleaseInfo::new);
        utilService.updateUserInfoToEntity(mReleaseInfo);
        releaseInfoMapper.updateById(mReleaseInfo);
        if (GeneralTool.isNotEmpty(releaseInfoAndItemDto.getReleaseInfoItemDtos())){
            releaseInfoAndItemDto.getReleaseInfoItemDtos().forEach(releaseInfoItemDto -> {
                mReleaseInfoItemService.editReleaseInfoItem(releaseInfoItemDto);
            });
        }

    }


}


<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.insurancecenter.mapper.CreditCardStatementMapper">


    <select id="selectTradeRecordPage" resultType="com.get.insurancecenter.vo.card.TradeRecordVo">
        select cs.*,
        o.insurance_num as insuranceNum,
        o.order_num as orderNum,
        o.insurant_name as insurantName,
        o.id as orderId,
        t.type_name as productTypeName
        from m_credit_card_statement cs
        left join m_insurance_order o
        on cs.relation_target_key = 'm_insurance_order' and cs.relation_target_id = o.id
        left join u_product_type t on o.fk_product_type_id = o.id
        <where>
            cs.fk_credit_card_id = #{param.creditCardId}
            <if test="param.businessType != null">
                and cs.business_type =
                #{param.businessType}
            </if>
            <if test="param.status != null">
                and cs.status =
                #{param.status}
            </if>
            <if test="param.startTime != null">
                and cs.gmt_create &gt;=
                #{param.startTime}
            </if>
            <if test="param.endTime != null">
                and cs.gmt_create &lt;=
                #{param.endTime}
            </if>
            <if test="param.insurantName != null and param.insurantName != ''">
                and o.insurant_name like concat('%',
                #{param.insurantName},
                '%'
                )
            </if>
        </where>
        order by cs.gmt_create desc
    </select>
</mapper>
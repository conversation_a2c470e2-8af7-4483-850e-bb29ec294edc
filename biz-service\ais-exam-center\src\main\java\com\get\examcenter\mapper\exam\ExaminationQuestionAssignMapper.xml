<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.examcenter.mapper.exam.ExaminationQuestionAssignMapper">
  <insert id="insert" parameterType="com.get.examcenter.entity.ExaminationQuestionAssign" useGeneratedKeys="true" keyProperty="id">
    insert into m_examination_question_assign (id, fk_examination_paper_id, target_type, 
      target_id, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkExaminationPaperId,jdbcType=BIGINT}, #{targetType,jdbcType=INTEGER}, 
      #{targetId,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.examcenter.entity.ExaminationQuestionAssign" useGeneratedKeys="true" keyProperty="id">
    insert into m_examination_question_assign
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkExaminationPaperId != null">
        fk_examination_paper_id,
      </if>
      <if test="targetType != null">
        target_type,
      </if>
      <if test="targetId != null">
        target_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkExaminationPaperId != null">
        #{fkExaminationPaperId,jdbcType=BIGINT},
      </if>
      <if test="targetType != null">
        #{targetType,jdbcType=INTEGER},
      </if>
      <if test="targetId != null">
        #{targetId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.examcenter.entity.ExaminationQuestionAssign">
    update m_examination_question_assign
    <set>
      <if test="fkExaminationPaperId != null">
        fk_examination_paper_id = #{fkExaminationPaperId,jdbcType=BIGINT},
      </if>
      <if test="targetType != null">
        target_type = #{targetType,jdbcType=INTEGER},
      </if>
      <if test="targetId != null">
        target_id = #{targetId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        view_order = #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="isActive != null">
        is_active = #{isActive,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.examcenter.entity.ExaminationQuestionAssign">
    update m_examination_question_assign
    set fk_examination_paper_id = #{fkExaminationPaperId,jdbcType=BIGINT},
      target_type = #{targetType,jdbcType=INTEGER},
      target_id = #{targetId,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      view_order = #{viewOrder,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="getListByPaperIdAndExist" resultType="com.get.examcenter.entity.ExaminationQuestionAssign">
      select * from(
      select meqa.target_id from m_examination_question_assign meqa where fk_examination_paper_id=#{fkExaminationPaperId}
      and EXISTS (select 1 from m_examination_question meq where meq.id=meqa.target_id)
      and meqa.target_type=1
      union ALL
      select meq.id target_id from m_examination_question meq where meq.fk_question_type_id =
      (select target_id from m_examination_question_assign meqa where fk_examination_paper_id=#{fkExaminationPaperId}
      and meqa.target_type=0)
      )aa where  EXISTS (select 1 from m_examination_question meq where meq.id=aa.target_id) order by target_id
    </select>
  <update id="updateActiveById">
    update m_examination_question_assign set is_active=0 where id =#{id}
  </update>
</mapper>
package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.entity.TravelClaimForm;
import com.get.financecenter.vo.TravelClaimFormVo;
import java.math.BigDecimal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TravelClaimFormMapper extends BaseMapper<TravelClaimForm> {


    TravelClaimFormVo getExistParentId(@Param("id") Long id);

    /**
     * 获取报销单总金额
     * @param id
     * @return
     */
    BigDecimal getTravelClaimFormTotalAmount(@Param("id") Long id);

    Integer getTravelClaimFormByTypeId(@Param("travelClaimFeeTypeId") Long travelClaimFeeTypeId);
}
package com.get.financecenter.enums;

import com.get.financecenter.vo.VouchTypeVo;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum VouchTypeEnum {

    /** 转账凭证 */
    TRANSFER("转", "转"),

    /** 现金收款凭证 */
    CASH_RECEIPT("现收", "现收"),

    /** 现金付款凭证 */
    CASH_PAYMENT("现付", "现付"),

    /** 银行收款凭证 */
    BANK_RECEIPT("银收", "银收"),

    /** 银行付款凭证 */
    BANK_PAYMENT("银付", "银付");

    private final String code;
    private final String codeName;

    VouchTypeEnum(String code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public String getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }

    /**
     * 根据编码获取枚举类型
     */
    public static String getValueByCode(String code) {
        for (VouchTypeEnum type : VouchTypeEnum.values()) {
            if (type.code.equals(code)) {
                return type.codeName;
            }
        }
        return null;
    }

    /**
     * 获取所有凭证类型选项（用于下拉菜单等）
     */
    public static List<VouchTypeVo> getOptions() {
        return Arrays.stream(values())
                .map(type ->{
                    VouchTypeVo selectEntity = new VouchTypeVo();
                    selectEntity.setCode(type.code);
                    selectEntity.setCodeName(type.codeName);
                    return selectEntity;
                })
                .collect(Collectors.toList());
    }
}

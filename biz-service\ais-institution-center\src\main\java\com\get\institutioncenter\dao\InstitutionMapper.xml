<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionMapper">
    <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.Institution">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_institution_type_id" jdbcType="BIGINT" property="fkInstitutionTypeId"/>
        <result column="fk_area_country_id" jdbcType="BIGINT" property="fkAreaCountryId"/>
        <result column="fk_area_state_id" jdbcType="BIGINT" property="fkAreaStateId"/>
        <result column="fk_area_city_id" jdbcType="BIGINT" property="fkAreaCityId"/>
        <result column="fk_currency_type_num" jdbcType="VARCHAR" property="fkCurrencyTypeNum"/>
        <result column="num" jdbcType="VARCHAR" property="num"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="name_chn" jdbcType="VARCHAR" property="nameChn"/>
        <result column="name_display" jdbcType="VARCHAR" property="nameDisplay"/>
        <result column="short_name" jdbcType="VARCHAR" property="shortName"/>
        <result column="short_name_chn" jdbcType="VARCHAR" property="shortNameChn"/>
        <result column="nature" jdbcType="VARCHAR" property="nature"/>
        <result column="established_date" jdbcType="VARCHAR" property="establishedDate"/>
        <result column="apply_date" jdbcType="VARCHAR" property="applyDate"/>
        <result column="apply_fee_min" jdbcType="DECIMAL" property="applyFeeMin"/>
        <result column="apply_fee_max" jdbcType="DECIMAL" property="applyFeeMax"/>
        <result column="website" jdbcType="VARCHAR" property="website"/>
        <result column="zip_code" jdbcType="VARCHAR" property="zipCode"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="is_kpi" jdbcType="BIT" property="isKpi"/>
        <result column="kpi_level" jdbcType="INTEGER" property="kpiLevel"/>
        <result column="is_active" jdbcType="BIT" property="isActive"/>
        <result column="public_level" jdbcType="VARCHAR" property="publicLevel"/>
        <result column="map_xy_gg" jdbcType="VARCHAR" property="mapXyGg"/>
        <result column="map_xy_bd" jdbcType="VARCHAR" property="mapXyBd"/>
        <result column="data_level" jdbcType="INTEGER" property="dataLevel"/>
        <result column="id_gea" jdbcType="VARCHAR" property="idGea"/>
        <result column="id_iae" jdbcType="VARCHAR" property="idIae"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.get.institutioncenter.entity.Institution">
        <result column="detail" jdbcType="LONGVARCHAR" property="detail"/>
    </resultMap>
    <!--  <resultMap id="JoinBaseResultMap" type="com.get.common.entity.fegin.InstitutionFeginDto">-->
    <!--    <result column="id" jdbcType="BIGINT" property="id" />-->
    <!--    <result column="fk_area_country_id" jdbcType="BIGINT" property="fkAreaCountryId" />-->
    <!--    <result column="fk_area_country_name" jdbcType="VARCHAR" property="fkAreaCountryName" />-->
    <!--    <result column="name" jdbcType="VARCHAR" property="name" />-->
    <!--  </resultMap>-->
    <sql id="Blob_Column_List">
        detail
    </sql>
    <insert id="insert" parameterType="com.get.institutioncenter.entity.Institution" keyProperty="id"
            useGeneratedKeys="true">
        insert into m_institution (id, fk_institution_type_id, fk_area_country_id,
                                   fk_area_state_id, fk_area_city_id, fk_currency_type_num,
                                   num, name, name_chn,name_display,
                                   short_name, short_name_chn, nature, established_date,
                                   apply_date, apply_fee_min, apply_fee_max,
                                   website, zip_code, address,
                                   is_kpi, kpi_level, is_active, public_level, data_level,map_xy_gg,map_xy_bd,
                                   id_gea, id_iae, gmt_create,
                                   gmt_create_user, gmt_modified, gmt_modified_user,
                                   detail)
        values (#{id,jdbcType=BIGINT}, #{fkInstitutionTypeId,jdbcType=BIGINT}, #{fkAreaCountryId,jdbcType=BIGINT},
                #{fkAreaStateId,jdbcType=BIGINT}, #{fkAreaCityId,jdbcType=BIGINT},
                #{fkCurrencyTypeNum,jdbcType=VARCHAR},
                #{num,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{nameChn,jdbcType=VARCHAR},#{nameDisplay,jdbcType=VARCHAR},
                #{shortName,jdbcType=VARCHAR}, #{shortNameChn,jdbcType=VARCHAR}, #{nature,jdbcType=VARCHAR},
                #{establishedDate,jdbcType=VARCHAR},
                #{applyDate,jdbcType=VARCHAR}, #{applyFeeMin,jdbcType=DECIMAL}, #{applyFeeMax,jdbcType=DECIMAL},
                #{website,jdbcType=VARCHAR}, #{zipCode,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
                #{isKpi,jdbcType=BIT}, #{kpiLevel,jdbcType=INTEGER}, #{isActive,jdbcType=BIT},
                #{publicLevel,jdbcType=VARCHAR}, #{dataLevel,jdbcType=INTEGER}, #{mapXyGg,jdbcType=VARCHAR}, #{mapXyBd,jdbcType=VARCHAR},
                #{idGea,jdbcType=VARCHAR}, #{idIae,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP},
                #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
                #{gmtModifiedUser,jdbcType=VARCHAR},
                #{detail,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.Institution" keyProperty="id"
            useGeneratedKeys="true">
        insert into m_institution
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkInstitutionTypeId != null">
                fk_institution_type_id,
            </if>
            <if test="fkAreaCountryId != null">
                fk_area_country_id,
            </if>
            <if test="fkAreaStateId != null">
                fk_area_state_id,
            </if>
            <if test="fkAreaCityId != null">
                fk_area_city_id,
            </if>
            <if test="fkCurrencyTypeNum != null">
                fk_currency_type_num,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="nameChn != null">
                name_chn,
            </if>
            <if test="nameDisplay != null">
                name_display,
            </if>
            <if test="shortName != null">
                short_name,
            </if>
            <if test="shortNameChn != null">
                short_name_chn,
            </if>
            <if test="nature != null">
                nature,
            </if>
            <if test="establishedDate != null">
                established_date,
            </if>
            <if test="applyDate != null">
                apply_date,
            </if>
            <if test="applyFeeMin != null">
                apply_fee_min,
            </if>
            <if test="applyFeeMax != null">
                apply_fee_max,
            </if>
            <if test="website != null">
                website,
            </if>
            <if test="zipCode != null">
                zip_code,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="isKpi != null">
                is_kpi,
            </if>
            <if test="kpiLevel != null">
                kpi_level,
            </if>
            <if test="isActive != null">
                is_active,
            </if>
            <if test="publicLevel != null">
                public_level,
            </if>
            <if test="dataLevel != null">
                data_level,
            </if>
            <if test="mapXyGg != null">
                map_xy_gg,
            </if>
            <if test="mapXyBd != null">
                map_xy_bd,
            </if>
            <if test="idGea != null">
                id_gea,
            </if>
            <if test="idIae != null">
                id_iae,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
            <if test="detail != null">
                detail,
            </if>
            <if test="k12Type != null">
                k12_type,
            </if>
            <if test="isChurch != null">
                is_church,
            </if>
            <if test="accommodationType != null">
                accommodation_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionTypeId != null">
                #{fkInstitutionTypeId,jdbcType=BIGINT},
            </if>
            <if test="fkAreaCountryId != null">
                #{fkAreaCountryId,jdbcType=BIGINT},
            </if>
            <if test="fkAreaStateId != null">
                #{fkAreaStateId,jdbcType=BIGINT},
            </if>
            <if test="fkAreaCityId != null">
                #{fkAreaCityId,jdbcType=BIGINT},
            </if>
            <if test="fkCurrencyTypeNum != null">
                #{fkCurrencyTypeNum,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameChn != null">
                #{nameChn,jdbcType=VARCHAR},
            </if>
            <if test="nameDisplay != null">
                #{nameDisplay,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="shortNameChn != null">
                #{shortNameChn,jdbcType=VARCHAR},
            </if>
            <if test="nature != null">
                #{nature,jdbcType=VARCHAR},
            </if>
            <if test="establishedDate != null">
                #{establishedDate,jdbcType=VARCHAR},
            </if>
            <if test="applyDate != null">
                #{applyDate,jdbcType=VARCHAR},
            </if>
            <if test="applyFeeMin != null">
                #{applyFeeMin,jdbcType=DECIMAL},
            </if>
            <if test="applyFeeMax != null">
                #{applyFeeMax,jdbcType=DECIMAL},
            </if>
            <if test="website != null">
                #{website,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="isKpi != null">
                #{isKpi,jdbcType=BIT},
            </if>
            <if test="kpiLevel != null">
                #{kpiLevel,jdbcType=INTEGER},
            </if>
            <if test="isActive != null">
                #{isActive,jdbcType=BIT},
            </if>
            <if test="publicLevel != null">
                #{publicLevel,jdbcType=VARCHAR},
            </if>
            <if test="dataLevel != null">
                #{dataLevel,jdbcType=BIT},
            </if>
            <if test="mapXyGg != null">
                #{mapXyGg,jdbcType=VARCHAR},
            </if>
            <if test="mapXyBd != null">
                #{mapXyBd,jdbcType=VARCHAR},
            </if>
            <if test="idGea != null">
                #{idGea,jdbcType=VARCHAR},
            </if>
            <if test="idIae != null">
                #{idIae,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
            <if test="detail != null">
                #{detail,jdbcType=LONGVARCHAR},
            </if>
            <if test="k12Type != null">
                #{k12Type,jdbcType=LONGVARCHAR},
            </if>
            <if test="isChurch != null">
                #{isChurch,jdbcType=LONGVARCHAR},
            </if>
            <if test="accommodationType != null">
                #{accommodationType,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <select id="getInstitutionIds" parameterType="java.lang.String" resultType="long">
        select i.id
        from m_institution i
        where i.name
                  like
              #{institutionName}  or i.name_chn
            like #{institutionName} or i.short_name
            like
            #{institutionName}  or i.short_name_chn
            like #{institutionName}

    </select>

    <select id="getInstitutionIdsByColumnName" resultType="java.lang.Long">
        SELECT id FROM m_institution WHERE ${columnName} IS NOT NULL AND ${columnName} != ""
    </select>

    <select id="getInstitutionById" resultType="com.get.institutioncenter.entity.Institution">
        SELECT * FROM m_institution WHERE id = #{id}
    </select>

    <select id="getInstitutionNameById" parameterType="java.lang.Long" resultType="string">
        select CASE WHEN IFNULL(name_chn, '') = '' THEN `name` ELSE CONCAT(`name`, '（', name_chn, '）') END fullName
        from m_institution i
        where i.id = #{id}
    </select>

    <select id="getCountryIdByInstitutionId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select fk_area_country_id
        from m_institution i
        where i.id = #{id}
    </select>
    <select id="getByVo" resultType="com.get.institutioncenter.vo.InstitutionVo">
        SELECT
        ANY_VALUE(i.id) AS id,
        ANY_VALUE(i.fk_institution_type_id) AS fk_institution_type_id,
        ANY_VALUE(i.fk_area_country_id) AS fk_area_country_id,
        ANY_VALUE(i.fk_area_state_id) AS fk_area_state_id,
        ANY_VALUE(i.fk_area_city_id) AS fk_area_city_id,
        ANY_VALUE(i.fk_currency_type_num) AS fk_currency_type_num,
        ANY_VALUE(i.num) AS num,
        ANY_VALUE(i.name) AS name,
        ANY_VALUE(i.name_chn) AS name_chn,
        ANY_VALUE(i.name_display) AS name_display,
        ANY_VALUE(i.short_name) AS short_name,
        ANY_VALUE(i.short_name_chn) AS short_name_chn,
        ANY_VALUE(i.nature) AS nature,
        ANY_VALUE(i.established_date) AS established_date,
        ANY_VALUE(i.apply_date) AS apply_date,
        ANY_VALUE(i.apply_fee_min) AS apply_fee_min,
        ANY_VALUE(i.apply_fee_max) AS apply_fee_max,
        ANY_VALUE(i.apply_fee_ref) AS apply_fee_ref,
        ANY_VALUE(i.apply_fee_cny) AS apply_fee_cny,
        ANY_VALUE(i.website) AS website,
        ANY_VALUE(i.zip_code) AS zip_code,
        ANY_VALUE(i.address) AS address,
        ANY_VALUE(i.detail) AS detail,
        ANY_VALUE(i.map_xy_gg) AS map_xy_gg,
        ANY_VALUE(i.map_xy_bd) AS map_xy_bd,
        ANY_VALUE(i.k12_type) AS k12_type,
        ANY_VALUE(i.accommodation_type) AS accommodation_type,
        ANY_VALUE(i.ranking_type) AS ranking_type,
        ANY_VALUE(i.is_church) AS is_church,
        ANY_VALUE(i.is_including_non_english) AS is_including_non_english,
        ANY_VALUE(i.is_kpi) AS is_kpi,
        ANY_VALUE(i.kpi_level) AS kpi_level,
        ANY_VALUE(i.is_active) AS is_active,
        ANY_VALUE(i.public_level) AS public_level,
        ANY_VALUE(i.data_level) AS data_level,
        ANY_VALUE(i.id_gea) AS id_gea,
        ANY_VALUE(i.id_iae) AS id_iae,
        ANY_VALUE(i.gmt_create) AS gmt_create,
        ANY_VALUE(i.gmt_create_user) AS gmt_create_user,
        ANY_VALUE(i.gmt_modified) AS gmt_modified,
        ANY_VALUE(i.gmt_modified_user) AS gmt_modified_user,
        ANY_VALUE(CASE WHEN IFNULL(i.name_chn,'')='' THEN i.`name` ELSE CONCAT(i.`name`,'（',i.name_chn,'）') END ) fullName
        <if test="institutionDto.fkInstitutionPermissionGroupId !=null and institutionDto.fkInstitutionPermissionGroupId !=''">
            ,ANY_VALUE(ripgi.id )AS fkInstitutionPermissionGroupInstitutionId
        </if>
        from
        m_institution i
        <if test="institutionDto.fkInstitutionPermissionGroupId !=null and institutionDto.fkInstitutionPermissionGroupId !=''">
            LEFT JOIN ais_permission_center.r_institution_permission_group_institution ripgi
            ON ripgi.fk_institution_id = i.id
            AND ripgi.fk_institution_permission_group_id = #{institutionDto.fkInstitutionPermissionGroupId}
        </if>
        <where>
            <if test="institutionDto.fkInstitutionTypeId!=null and institutionDto.fkInstitutionTypeId !=''">
                and i.fk_institution_type_id = #{institutionDto.fkInstitutionTypeId}
            </if>
            <if test="institutionDto.fkAreaCountryId!=null and institutionDto.fkAreaCountryId !=''">
                and i.fk_area_country_id = #{institutionDto.fkAreaCountryId}
            </if>
            <if test="institutionDto.countryIds!=null and institutionDto.countryIds.size()>0">
                and i.fk_area_country_id IN
                <foreach collection="institutionDto.countryIds" item="countryId" index="index" open="(" separator=","
                         close=")">
                    #{countryId}
                </foreach>
            </if>
            <if test="institutionDto.isActive!=null">
                and i.is_active = #{institutionDto.isActive}
            </if>
            <if test="institutionDto.publicLevel!=null and institutionDto.publicLevel !=''">
                and i.public_level like concat("%",#{institutionDto.publicLevel},"%")
            </if>
            <!--解决传0不识别-->
            <if test="institutionDto.dataLevel == 0 or institutionDto.dataLevel != null and institutionDto.dataLevel != ''">
                and i.data_level = #{institutionDto.dataLevel}
            </if>
<!--            <if test="institutionDto.keyWord!=null and institutionDto.keyWord !=''">-->
<!--                and (i.num like concat("%",#{institutionDto.keyWord},"%") or i.name like-->
<!--                concat("%",#{institutionDto.keyWord},"%") or i.name_chn like-->
<!--                concat("%",#{institutionDto.keyWord},"%") or i.short_name like concat("%",#{institutionDto.keyWord},"%") or-->
<!--                i.short_name_chn like concat("%",#{institutionDto.keyWord},"%"))-->
<!--            </if>-->
<!--            <if test="institutionDto.institutionKeyWord!=null and institutionDto.institutionKeyWord !=''">-->
<!--                and (i.num like concat("%",#{institutionDto.institutionKeyWord},"%") or i.name like-->
<!--                concat("%",#{institutionDto.institutionKeyWord},"%") or i.name_chn like-->
<!--                concat("%",#{institutionDto.institutionKeyWord},"%") or i.short_name like concat("%",#{institutionDto.institutionKeyWord},"%") or-->
<!--                i.short_name_chn like concat("%",#{institutionDto.institutionKeyWord},"%"))-->
<!--            </if>-->
            <if test="institutionDto.keyWord != null and institutionDto.keyWord != ''">
                and (
                LOWER(i.num) LIKE LOWER(CONCAT("%", #{institutionDto.keyWord}, "%")) OR
                LOWER(i.name) LIKE LOWER(CONCAT("%", #{institutionDto.keyWord}, "%")) OR
                LOWER(i.name_chn) LIKE LOWER(CONCAT("%", #{institutionDto.keyWord}, "%")) OR
                LOWER(i.short_name) LIKE LOWER(CONCAT("%", #{institutionDto.keyWord}, "%")) OR
                LOWER(i.short_name_chn) LIKE LOWER(CONCAT("%", #{institutionDto.keyWord}, "%"))
                )
            </if>
            <if test="institutionDto.institutionKeyWord != null and institutionDto.institutionKeyWord != ''">
                and (
                LOWER(i.num) LIKE LOWER(CONCAT("%", #{institutionDto.institutionKeyWord}, "%")) OR
                LOWER(i.name) LIKE LOWER(CONCAT("%", #{institutionDto.institutionKeyWord}, "%")) OR
                LOWER(i.name_chn) LIKE LOWER(CONCAT("%", #{institutionDto.institutionKeyWord}, "%")) OR
                LOWER(i.short_name) LIKE LOWER(CONCAT("%", #{institutionDto.institutionKeyWord}, "%")) OR
                LOWER(i.short_name_chn) LIKE LOWER(CONCAT("%", #{institutionDto.institutionKeyWord}, "%"))
                )
            </if>

<!--            <if test="institutionDto.fkInstitutionPermissionGroupId !=null and institutionDto.fkInstitutionPermissionGroupId !=''">-->
<!--                <if test="institutionDto.isBind == 0">-->
<!--                    AND NOT EXISTS (SELECT 1 FROM ais_permission_center.r_institution_permission_group_institution AS ripgi WHERE ripgi.fk_institution_id = i.id AND ripgi.fk_institution_permission_group_id = #{institutionDto.fkInstitutionPermissionGroupId})-->
<!--                </if>-->
<!--                <if test="institutionDto.isBind == 1">-->
<!--                    AND EXISTS (SELECT 1 FROM ais_permission_center.r_institution_permission_group_institution AS ripgi WHERE ripgi.fk_institution_id = i.id AND ripgi.fk_institution_permission_group_id = #{institutionDto.fkInstitutionPermissionGroupId} AND i.is_active = 1)-->
<!--                </if>-->
<!--            </if>-->
            <if test="institutionDto.fkInstitutionPermissionGroupId != null and institutionDto.fkInstitutionPermissionGroupId != ''">
                <choose>
                    <when test="institutionDto.isBind == 0">
                        AND ripgi.id IS NULL  <!-- 未绑定 -->
                        AND i.is_active = 1
                    </when>
                    <when test="institutionDto.isBind == 1">
                        AND ripgi.id IS NOT NULL  <!-- 已绑定 -->
                        AND i.is_active = 1
                    </when>
                </choose>
            </if>
            <!-- <if test="countryIds != null and countryIds.size()>0">
                 AND fk_area_country_id IN
                 <foreach collection="countryIds" item="countryId" index="index" open="(" separator="," close=")">
                     #{countryId,jdbcType=INTEGER}
                 </foreach>
             </if>-->
        </where>
        GROUP BY i.id
        ORDER BY fullName COLLATE utf8mb4_bin, ANY_VALUE(i.is_active) desc
    </select>

    <select id="getCountByVo" resultType="java.lang.Integer">
        select count(*) from m_institution
        <where>
            <if test="fkInstitutionTypeId!=null and fkInstitutionTypeId !=''">
                and fk_institution_type_id = #{fkInstitutionTypeId}
            </if>
            <if test="fkAreaCountryId!=null and fkAreaCountryId !=''">
                and fk_area_country_id = #{fkAreaCountryId}
            </if>
            <if test="isActive!=null and isActive !=''">
                and is_active = #{isActive}
            </if>
            <if test="keyWord!=null and keyWord !=''">
                and (num like concat("%",#{keyWord},"%") or name like concat("%",#{keyWord},"%") or name_chn like
                concat("%",#{keyWord},"%"))
            </if>

        </where>
    </select>
    <select id="getInstitutionList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT id,
               NAME,
               name_chn,
               concat(
                       IF
                           (is_active = 0, '【无效】', ''),
                       CASE
                           WHEN IFNULL(name_chn, '') = '' THEN
                               NAME
                           ELSE CONCAT(NAME, '（', name_chn, '）')
                           END
                   ) fullName,is_active status
        FROM m_institution
        ORDER BY is_active DESC, name ASC
    </select>
    <select id="getProviderInstitutionList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT i.id,
        i.NAME,
        i.name_chn,
        concat(
        IF
        (i.is_active = 0, '【无效】', ''),
        CASE
        WHEN IFNULL(i.name_chn, '') = '' THEN
        i.NAME
        ELSE CONCAT(i.NAME, '（', i.name_chn, '）')
        END
        ) fullName,i.is_active status
        FROM m_institution AS i
        INNER JOIN r_institution_provider_institution AS ripi ON ripi.fk_institution_id = i.id
        INNER JOIN m_institution_provider AS mip ON mip.id = ripi.fk_institution_provider_id
        INNER JOIN r_institution_provider_company AS ripc ON ripc.fk_institution_provider_id = mip.id
        WHERE ripc.fk_company_id IN
        <foreach collection="companyIds" item="companyId" index="index" open="(" separator="," close=")">
            #{companyId,jdbcType=INTEGER}
        </foreach>
        AND i.fk_area_country_id = #{countryId}
        GROUP BY i.id
        ORDER BY i.is_active DESC, i.name ASC
    </select>
    <select id="getInstitutionListByCountryId" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT id,
               NAME,
               name_chn,
               concat(
                       IF
                           (is_active = 0, '【无效】', ''),
                       CASE
                           WHEN IFNULL(name_chn, '') = '' THEN
                               NAME
                           ELSE CONCAT(NAME, '（', name_chn, '）')
                           END
                   ) fullName,is_active status
        FROM m_institution
        WHERE fk_area_country_id = #{countryId}
        ORDER BY is_active DESC, name ASC
    </select>

    <select id="getInstitutionListByCountryIdList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT id,
        NAME,
        name_chn,
        concat(
        IF
        (is_active = 0, '【无效】', ''),
        CASE
        WHEN IFNULL(name_chn, '') = '' THEN
        NAME
        ELSE CONCAT(NAME, '（', name_chn, '）')
        END
        ) fullName,is_active status
        FROM m_institution
        <where>
            <if test="fkCountryIdList != null and fkCountryIdList.size()>0">
                fk_area_country_id IN
                <foreach collection="fkCountryIdList" item="fkCountryId" open="(" separator="," close=")">
                    #{fkCountryId}
                </foreach>
            </if>
        </where>
        ORDER BY is_active DESC, name ASC
    </select>

    <select id="getInstitutionListByProviderId" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select i.id,
               i.name,
               i.name_chn,
               concat(
                       IF
                           (i.is_active = 0, '【无效】', ''),
                       CASE
                           WHEN IFNULL(i.name_chn, '') = '' THEN
                               i.NAME
                           ELSE CONCAT(i.NAME, '（', i.name_chn, '）')
                           END
                   ) fullName,i.is_active status
        from m_institution i
                 left join r_institution_provider_institution ipi on ipi.fk_institution_id = i.id
        where ipi.fk_institution_provider_id = #{providerId}

    </select>

    <select id="getInstitutions" resultType="com.get.institutioncenter.vo.InstitutionVo">
        SELECT DISTINCT r.id fkId,
        m.*,
        (
        SELECT GROUP_CONCAT(sub_mc.id ORDER BY sub_mc.view_order DESC SEPARATOR ',')
        FROM ais_permission_center.m_company sub_mc
        WHERE FIND_IN_SET(sub_mc.id, r.fk_company_ids) > 0
        ) AS fk_company_ids,
<!--        r.fk_company_ids,-->
        CASE WHEN IFNULL(m.name_chn,'')='' THEN m.`name` ELSE CONCAT(m.`name`,'（',m.name_chn,'）') END fullName,
        r.is_active as isBindingActive
        FROM m_institution m LEFT JOIN r_institution_provider_institution r on m.id=r.fk_institution_id


        WHERE
        ( 1=2
        <foreach collection="companyIds" item="companyId" index="index">
            or FIND_IN_SET(#{companyId}, r.fk_company_ids)
        </foreach>
        )

        <if test="institutionDto.fkProviderId!=null and institutionDto.fkProviderId !=''">
                and r.fk_institution_provider_id=#{institutionDto.fkProviderId}
        </if>
        <if test="institutionDto.fkAreaCountryId!=null and institutionDto.fkAreaCountryId !=''">
                and m.fkAreaCountryId=#{institutionDto.fkAreaCountryId}
        </if>
        <if test="institutionDto.keyWord!=null and institutionDto.keyWord !=''">
                AND (
                m.name LIKE CONCAT(CONCAT('%', #{institutionDto.keyWord}), '%')
                OR m.name_chn LIKE CONCAT('%',#{institutionDto.keyWord},'%')
                OR num LIKE CONCAT(CONCAT('%', #{institutionDto.keyWord}), '%')
                )
        </if>
        <if test="institutionDto.isBindingActive!=null and institutionDto.isBindingActive !=''">
            and r.is_active = #{institutionDto.isBindingActive}
        </if>
        ORDER BY
        r.is_active desc,
        m.fk_area_country_id asc,
        CONVERT ( m.name USING gbk ) ASC
    </select>
    <select id="getBridgeInstitutionSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select i.id,i.name,i.name_chn as nameChn,concat(
        IF
        ( i.is_active = 0, '【无效】', '' ),
        CASE
        WHEN IFNULL( i.name_chn, '' ) = '' THEN
        i.NAME ELSE CONCAT( i.NAME, '（', i.name_chn, '）' )
        END
        ) fullName,i.is_active status from m_institution i left join u_institution_type it on i.fk_institution_type_id =
        it.id
        <where>
            <if test="ids != null and ids.size()>0">
                and i.id not in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY
        i.is_active DESC,i.name ASC
    </select>
    <select id="getNonBridgeInstitutionSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select i.id,i.name,i.name_chn as nameChn,concat(
        IF
        ( i.is_active = 0, '【无效】', '' ),
        CASE
        WHEN IFNULL( i.name_chn, '' ) = '' THEN
        i.NAME ELSE CONCAT( i.NAME, '（', i.name_chn, '）' )
        END
        ) fullName,i.is_active status from m_institution i left join u_institution_type it on i.fk_institution_type_id =
        it.id
        <where>
            <if test="ids != null and ids.size()>0">
                and i.id not in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY
        i.is_active DESC,i.name ASC
    </select>
    <select id="getNonBridgeInstitutionSelectByCourse" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select i.id,i.name,i.name_chn as nameChn,concat(
        IF
        ( i.is_active = 0, '【无效】', '' ),
        CASE
        WHEN IFNULL( i.name_chn, '' ) = '' THEN
        i.NAME ELSE CONCAT( i.NAME, '（', i.name_chn, '）' )
        END
        ) fullName,i.is_active status from
        r_institution_pathway AS ip
        Left JOIN m_institution AS i ON i.id = ip.fk_institution_id
        Left JOIN u_institution_type AS it ON i.fk_institution_type_id = it.id
        <where>
            <!--      <if test="ids != null and ids.size()>0">-->
            <!--        and i.id not in-->
            <!--        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">-->
            <!--          #{id}-->
            <!--        </foreach>-->
            <!--      </if>-->
            <if test="pathwayInstitutionId != null">
                AND ip.fk_institution_id_pathway = #{pathwayInstitutionId}
            </if>
        </where>
        ORDER BY
        i.is_active DESC,i.name ASC
    </select>


    <select id="getBridgeInstitutionSelectByCourse" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select i.id,i.name,i.name_chn as nameChn,concat(
        IF
        ( i.is_active = 0, '【无效】', '' ),
        CASE
        WHEN IFNULL( i.name_chn, '' ) = '' THEN
        i.NAME ELSE CONCAT( i.NAME, '（', i.name_chn, '）' )
        END
        ) fullName ,i.is_active status from r_institution_pathway AS ip
        INNER JOIN m_institution i ON i.id = ip.fk_institution_id_pathway
        INNER JOIN u_institution_type it on i.fk_institution_type_id = it.id
        <where>
            <if test="institutionId != null">
                AND ip.fk_institution_id = #{institutionId}
            </if>
        </where>
        ORDER BY
        i.is_active DESC,i.name ASC
    </select>

    <select id="getInstitutionFullNameById" parameterType="java.lang.Long" resultType="string">
        select CASE WHEN IFNULL(name_chn, '') = '' THEN `name` ELSE CONCAT(`name`, '（', name_chn, '）') END fullName
        from m_institution i
        where i.id = #{id}
    </select>
    <select id="getInstitutionCountryInfoByInstitutionIds" resultType="com.get.institutioncenter.vo.InstitutionVo">
        SELECT i.*,CASE WHEN IFNULL(ac.name_chn,'')='' THEN ac.name ELSE CONCAT(ac.name,'（',ac.name_chn,'）') END
        fkAreaCountryName FROM m_institution i
        LEFT JOIN u_area_country AS ac ON ac.id = i.fk_area_country_id
        <where>
            <if test="institutionIds != null and institutionIds.size()>0">
                i.id IN
                <foreach collection="institutionIds" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getCountryByInstitutionIdSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
        ac.id,
        ac.num,
        ac.NAME,
        ac.name_chn,
        CASE WHEN IFNULL( ac.name_chn, '' )= '' THEN ac.NAME ELSE CONCAT( ac.NAME, '（', ac.name_chn, '）' ) END fullName
        FROM
        u_area_country AS ac
        INNER JOIN m_institution AS i ON i.fk_area_country_id = ac.id
        <where>
            <if test="id!=null and id !=''">
                i.id = #{id}
            </if>
        </where>
        ORDER BY
        view_order DESC
    </select>
    <select id="getCountByInstitutionId" resultType="com.get.institutioncenter.vo.InstitutionVo">
        select (select COUNT(fk_institution_id)as zoneNum from m_institution_zone where fk_institution_id=#{id})as zoneNum,
               (select COUNT(fk_institution_id)as facultyNum from m_institution_faculty where fk_institution_id=#{id})as facultyNum,
               (select COUNT(fk_institution_id)as courseNum from m_institution_course where fk_institution_id=#{id})as courseNum,
               (select COUNT(fk_institution_id)as faqNum from m_institution_faq where fk_institution_id=#{id})as faqNum,
               (select COUNT(fk_institution_id)as alumnusNum from m_institution_alumnus where fk_institution_id=#{id})as alumnusNum,
               (select COUNT(fk_institution_id)as infoNum from m_institution_info where fk_institution_id=#{id})as infoNum,
               (select COUNT(fk_table_id)as scholarshipNum from r_institution_course_app_info where fk_table_name = 'm_institution_app_fee'
               AND fk_table_id_type=#{id} AND fk_table_name_type='m_institution')as appFeeNum,
               (select COUNT(fk_table_id)as scholarshipNum from r_institution_course_app_info where fk_table_name = 'm_institution_scholarship'
               AND fk_table_id_type=#{id} AND fk_table_name_type='m_institution')as scholarshipNum,
               (select COUNT(fk_table_id)as scholarshipNum from r_institution_course_app_info where fk_table_name = 'm_institution_deadline_info'
               AND fk_table_id_type=#{id} AND fk_table_name_type='m_institution')as deadlineInfoNum
    </select>
    <select id="getInstitutionSfList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
              SELECT id,
               NAME,
               name_chn,
               short_name,
               concat(
                       IF
                           (is_active = 0, '【无效】', ''),
                       CASE
                           WHEN IFNULL(name_chn, '') = '' and IFNULL(short_name,'')=''  THEN
                               NAME
                           when name_chn is not null and IFNULL(short_name,'')='' then  CONCAT(NAME,'（', name_chn, '）')

                           ELSE CONCAT(NAME, ',',short_name,'（',name, name_chn, '）')
                           END
                   ) fullName,is_active status
        FROM m_institution
        ORDER BY is_active DESC, name ASC;
    </select>
    <select id="getInstitutionsByNameEnOrNameZh" resultType="java.lang.Long">
select id from m_institution where
name like concat('%',#{schoolName},'%')
or name_chn like concat('%',#{schoolName},'%')
or short_name like concat('%',#{schoolName},'%')
or short_name_chn like concat('%',#{schoolName},'%')
or CONCAT(name,IFNULL(CONCAT('（',name_chn,'）'),'')) like concat('%',#{schoolName},'%')
    </select>
    <select id="getInstitutionsByNameEnOrNameZh2" resultType="java.lang.Long">
        select id from m_institution where 1=1
        <if test="schoolName!=null and  schoolName!=''">
            and name like concat('%',#{schoolName},'%')
            or name_chn like concat('%',#{schoolName},'%')
            or short_name like concat('%',#{schoolName},'%')
            or short_name_chn like concat('%',#{schoolName},'%')
            or CONCAT(name,IFNULL(CONCAT('（',name_chn,'）'),'')) like concat('%',#{schoolName},'%')
        </if>
        <if test="fkCountryId!=null and fkCountryId!=''">
            and fk_area_country_id=#{fkCountryId}
        </if>
    </select>
    <select id="getInstitutionsObjByNameEnOrNameZh" resultType="com.get.institutioncenter.vo.InstitutionVo">
select id,CONCAT(name,IFNULL(CONCAT('（',name_chn,'）'),''))  as fullName from m_institution where
name like concat('%',#{schoolName},'%')
or name_chn like concat('%',#{schoolName},'%')
or short_name like concat('%',#{schoolName},'%')
or short_name_chn like concat('%',#{schoolName},'%')
or CONCAT(name,IFNULL(CONCAT('（',name_chn,'）'),'')) like concat('%',#{schoolName},'%')
    </select>
    <select id="getInstitutionsByNameEnOrNameZhAndCountry" resultType="java.lang.Long">
        select id from m_institution where fk_area_country_id=#{fkCountryId}
        <if test="schoolName!='' and schoolName!=null">
            and(
            name like concat('%',#{schoolName},'%')
            or name_chn like concat('%',#{schoolName},'%')
            or short_name like concat('%',#{schoolName},'%')
            or short_name_chn like concat('%',#{schoolName},'%')
            or CONCAT(name,IFNULL(CONCAT('（',name_chn,'）'),'')) like concat('%',#{schoolName},'%'))
        </if>
    </select>

    <select id="getInstitutionByName" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select i.id,concat_ws(',', if(LENGTH(trim(short_name)) > 1, short_name, null), name, name_chn) as
        full_name,i.name,i.name_chn as old_name
        from m_institution i
        where 1=1
        <if test="institutionByNameDto.institutionName!=null and institutionByNameDto.institutionName!=''">
            and (LOWER(i.name) like concat('%', #{institutionByNameDto.institutionName}, '%')
            or LOWER(i.name_chn) like concat('%', #{institutionByNameDto.institutionName}, '%')
            or LOWER(i.short_name) like concat('%', #{institutionByNameDto.institutionName}, '%')
            or LOWER(i.short_name_chn) like concat('%', #{institutionByNameDto.institutionName}, '%'))
        </if>
        <if test="institutionByNameDto.institutionIds!=null and institutionByNameDto.institutionIds.size()>0">
            and i.id IN
            <foreach collection="institutionByNameDto.institutionIds" item="id" index="index" open="(" separator=","
                     close=")">
                #{id}
            </foreach>
        </if>
        <if test="institutionByNameDto.fkAreaCountryIds!=null and institutionByNameDto.fkAreaCountryIds.size()>0">
            and i.fk_area_country_id IN
            <foreach collection="institutionByNameDto.fkAreaCountryIds" item="id" index="index" open="(" separator=","
                     close=")">
                #{id}
            </foreach>
        </if>
        limit 20
    </select>
    <select id="getProviderInsByOfferId" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            i.id,
            i. NAME,
            i.name_chn,
            concat(

                IF (
                    i.is_active = 0,
                    '【无效】',
                    ''
                ),
                CASE
            WHEN IFNULL(i.name_chn, '') = '' THEN
                i. NAME
            ELSE
                CONCAT(
                    i. NAME,
                    '（',
                    i.name_chn,
                    '）'
                )
            END
            ) fullName,
            i.is_active STATUS
        FROM
            m_institution AS i
        INNER JOIN r_institution_provider_institution AS ripi ON ripi.fk_institution_id = i.id
        INNER JOIN m_institution_provider AS mip ON mip.id = ripi.fk_institution_provider_id
        INNER JOIN ais_sale_center.m_student_offer_item f ON f.fk_institution_provider_id = mip.id
        WHERE
            f.id = #{id}
        GROUP BY i.id
        ORDER BY i.is_active DESC, i.name ASC
    </select>
    <select id="getRankingTypeCount" resultType="com.get.institutioncenter.dto.CaseStudyResultsDto$Statistics">
        SELECT DISTINCT
        IF(m.ranking_type is null or m.ranking_type='','其他',m.ranking_type) AS `name`,
        IFNULL(s.sum, 0) AS num,1 as type
        FROM
        m_institution AS m
        LEFT JOIN (
        SELECT
        IF(ranking_type is null or ranking_type='','其他',ranking_type) as ranking_type,
        COUNT(*) AS sum
        FROM
        m_institution
        <if test="ids!=null and ids.size>0">
            WHERE id IN
            <foreach collection="ids" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        ranking_type
        ) AS s ON IF(m.ranking_type is null or m.ranking_type='','其他',m.ranking_type) = s.ranking_type
        GROUP BY
        m.ranking_type
    </select>
    <select id="getInstitutionListByKeyword" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            id,
            NAME AS NAME,
            name_chn AS nameChn,
            CASE
            WHEN IFNULL(name_chn, '') = '' THEN
            `name`
            ELSE
            CONCAT(
            `name`,
            '（',
            name_chn,
            '）'
            )
            END fullName
        FROM
        m_institution
        WHERE
            1=1
            <if test="countryId!=null and countryId!=''">
                AND fk_area_country_id = #{countryId}
            </if>
            <if test="keyword!=null and keyword!=''">
                AND (`name` LIKE concat('%',#{keyword},'%') OR name_chn LIKE concat('%',#{keyword},'%')
                         OR concat(name,"（",name_chn,"）") LIKE concat('%',#{keyword},'%')
                    )
            </if>
        LIMIT 50
    </select>

    <select id="getInstitutionIdsByKeyword" resultType="java.lang.Long">
        SELECT
            id
        FROM
        m_institution
        <where>
            <if test="keyword!=null and keyword!=''">
                AND (
                `name` LIKE concat('%',#{keyword},'%')
                OR name_chn LIKE concat('%',#{keyword},'%')
                OR concat(name,"（",name_chn,"）") LIKE concat('%',#{keyword},'%')
                )
            </if>
        </where>
    </select>

    <select id="getLikeInstitutionIds" resultType="java.lang.Long">
        SELECT DISTINCT i.id
         FROM m_institution i
         LEFT JOIN r_institution_provider_institution AS ripi ON i.id = ripi.fk_institution_id
         LEFT JOIN m_institution_provider AS mip ON mip.id = ripi.fk_institution_provider_id
         LEFT JOIN r_institution_provider_company AS ripc ON ripc.fk_institution_provider_id = mip.id
         <where>
             <if test="institutionApplicationStaticsDto.fkInstitutionName != null and institutionApplicationStaticsDto.fkInstitutionName != ''">
                 AND (
                 i.`name` LIKE CONCAT("%",#{institutionApplicationStaticsDto.fkInstitutionName},"%")
                 OR i.name_chn LIKE CONCAT("%",#{institutionApplicationStaticsDto.fkInstitutionName},"%")
                 )
             </if>
             <if test="institutionApplicationStaticsDto.fkInstitutionProviderName != null and institutionApplicationStaticsDto.fkInstitutionProviderName != ''">
                 AND (
                 mip.`name` LIKE CONCAT("%",#{institutionApplicationStaticsDto.fkInstitutionProviderName},"%")
                 OR mip.name_chn LIKE CONCAT("%",#{institutionApplicationStaticsDto.fkInstitutionProviderName},"%")
                 )
             </if>
             <if test="institutionApplicationStaticsDto.fkCompanyIdList != null and institutionApplicationStaticsDto.fkCompanyIdList.size()>0">
                 AND ripc.fk_company_id IN
                 <foreach collection="institutionApplicationStaticsDto.fkCompanyIdList" item="fkCompanyId" open="(" separator="," close=")">
                     #{fkCompanyId}
                 </foreach>
             </if>
         </where>
    </select>

    <select id="getInstitutionProviderList" resultType="com.get.institutioncenter.vo.ApplicationStatisticsProviderVo">
        SELECT fkInstitutionId,fkCompanyIds,fkInstitutionProviderId,fkInstitutionProviderName
        FROM(
            SELECT
             ripi.fk_institution_id AS fkInstitutionId,
             GROUP_CONCAT(ripc.fk_company_id) AS fkCompanyIds,
             mip.id AS fkInstitutionProviderId,
             CASE WHEN IFNULL(mip.name_chn, '') = '' THEN CONCAT(mip.name,'【',(CASE WHEN ripi.is_active = 1 THEN '绑定' ELSE '解绑' END),'】')
                  ELSE  CONCAT(mip.name,'（',mip.name_chn,'）','【',(CASE WHEN ripi.is_active = 1 THEN '绑定' ELSE '解绑' END),'】')
                  END fkInstitutionProviderName
             FROM r_institution_provider_institution AS ripi
             LEFT JOIN m_institution_provider AS mip ON mip.id = ripi.fk_institution_provider_id
             LEFT JOIN r_institution_provider_company AS ripc ON ripc.fk_institution_provider_id = mip.id
            <where>
                <if test="institutionApplicationStaticsDto.fkInstitutionIds != null and institutionApplicationStaticsDto.fkInstitutionIds.size() > 0">
                    AND ripi.fk_institution_id IN
                    <foreach collection="institutionApplicationStaticsDto.fkInstitutionIds" item="fkInstitutionId" open="(" separator="," close=")">
                        #{fkInstitutionId}
                    </foreach>
                </if>
                <if test="institutionApplicationStaticsDto.fkInstitutionProviderName != null and institutionApplicationStaticsDto.fkInstitutionProviderName != ''">
                    AND (
                    mip.`name` LIKE CONCAT("%",#{institutionApplicationStaticsDto.fkInstitutionProviderName},"%")
                    OR mip.name_chn LIKE CONCAT("%",#{institutionApplicationStaticsDto.fkInstitutionProviderName},"%")
                    )
                </if>
                <!-- 集团过滤 -->
                <if test="institutionApplicationStaticsDto.fkInstitutionGroupIds != null and institutionApplicationStaticsDto.fkInstitutionGroupIds.size()>0">
                    AND mip.fk_institution_group_id IN
                    <foreach collection="institutionApplicationStaticsDto.fkInstitutionGroupIds" item="fkInstitutionGroupId" open="(" separator="," close=")">
                        #{fkInstitutionGroupId}
                    </foreach>
                </if>
            </where>
            GROUP BY  ripi.fk_institution_id,mip.id
        )AS t
        <where>
            <if test="institutionApplicationStaticsDto.fkCompanyIdList != null and institutionApplicationStaticsDto.fkCompanyIdList.size() > 0">
               <foreach collection="institutionApplicationStaticsDto.fkCompanyIdList" item="fkCompanyId"  separator="or">
                   FIND_IN_SET(#{fkCompanyId},t.fkCompanyIds)
               </foreach>
            </if>
        </where>
    </select>

    <select id="getInstitutionDtoList" resultType="com.get.institutioncenter.vo.InstitutionApplicationStatisticsVo">
        SELECT
         i.id AS fkInstitutionId,
         CASE WHEN IFNULL(i.name_chn, '') = '' THEN i.`name`
              ELSE CONCAT(i.`name`,'（',i.name_chn,'）')
              END fkInstitutionName
         FROM m_institution i
         LEFT JOIN r_institution_provider_institution ripi ON i.id = ripi.fk_institution_id
         LEFT JOIN m_institution_provider mip ON ripi.fk_institution_provider_id = mip.id
         LEFT JOIN m_institution_group mig ON mip.fk_institution_group_id = mig.id
         <where>
             <if test="institutionApplicationStaticsDto.fkInstitutionIds != null and institutionApplicationStaticsDto.fkInstitutionIds.size() > 0">
                 AND i.id IN
                 <foreach collection="institutionApplicationStaticsDto.fkInstitutionIds" item="fkInstitutionId" open="(" separator="," close=")">
                     #{fkInstitutionId}
                 </foreach>
             </if>
             <!-- 集团过滤 -->
             <if test="institutionApplicationStaticsDto.fkInstitutionGroupIds != null and institutionApplicationStaticsDto.fkInstitutionGroupIds.size()>0">
                 AND mig.id IN
                 <foreach collection="institutionApplicationStaticsDto.fkInstitutionGroupIds" item="fkInstitutionGroupId" open="(" separator="," close=")">
                     #{fkInstitutionGroupId}
                 </foreach>
             </if>
         </where>
        GROUP BY i.id
    </select>

    <select id="getNewsInstitutionSfList" resultType="com.get.core.mybatis.base.BaseSelectEntity">

        SELECT mi.id,
               mi.NAME,
               mi.name_chn,
               mi.short_name,
               concat(
                       IF
                           (mi.is_active = 0, '【无效】', ''),
                       CASE
                           WHEN IFNULL(mi.name_chn, '') = '' and IFNULL(mi.short_name,'')=''  THEN
                               mi.NAME
                           when mi.name_chn is not null and IFNULL(mi.short_name,'')='' then  CONCAT(mi.NAME,'（', mi.name_chn, '）')

                           ELSE CONCAT(mi.NAME, ',',mi.short_name,'（',mi.name, mi.name_chn, '）')
                           END
                   ) fullName,mi.is_active status
        FROM m_institution AS mi
        INNER JOIN r_news_type AS rnt ON rnt.fk_table_id = mi.id AND rnt.fk_table_name = 'm_institution'
        INNER JOIN s_news AS sn ON sn.id = rnt.fk_table_id
        GROUP BY mi.id
        ORDER BY is_active DESC, name ASC

    </select>
    <select id="getAiInstitutionInfo" resultType="com.get.institutioncenter.vo.AiInstitutionInfoVo">
        SELECT
            mi.id,
            mi.NAME AS institutionName,
            mi.name_chn AS institutionNameChn,
            uit.type_name_chn AS institutionTypeName,
            uac.name_chn AS countryName,
            uas.name_chn AS stateName
        FROM
            m_institution AS mi
                LEFT JOIN u_institution_type AS uit ON uit.id = mi.fk_institution_type_id
                LEFT JOIN ais_institution_center.u_area_country AS uac ON uac.id = mi.fk_area_country_id
                LEFT JOIN ais_institution_center.u_area_state AS uas ON uas.id = mi.fk_area_state_id
        WHERE mi.is_active = 1
            AND (
                REPLACE(LOWER(mi.`name`), ' ', '') like REPLACE(LOWER(concat('%', #{aiInstitutionDto.institutionName}, '%')), ' ', '')
                OR REPLACE(LOWER(mi.`name_chn`), ' ', '') like REPLACE(LOWER(concat('%', #{aiInstitutionDto.institutionName}, '%')), ' ', '')
            )
        limit 5
    </select>

    <select id="getAiCourseInfo" resultType="com.get.institutioncenter.vo.AiCourseVo">
        SELECT
            mic.fk_institution_id AS institutionId,
            mic.NAME AS courseName,
            mic.name_chn AS courseNameChn,
            GROUP_CONCAT(uct.type_name_chn) AS courseType,
            GROUP_CONCAT(uml.level_name_chn) AS levelNameChn
        FROM
            m_institution_course AS mic
                LEFT JOIN r_institution_course_type AS rict ON rict.fk_institution_course_id = mic.id
                LEFT JOIN u_course_type AS uct ON uct.id = rict.fk_course_type_id
                LEFT JOIN r_institution_course_major_level AS ricml ON ricml.fk_institution_course_id = mic.id
                LEFT JOIN u_major_level AS uml ON uml.id = ricml.fk_major_level_id
        WHERE mic.is_active = 1
          AND mic.fk_institution_id IN
        <foreach collection="institutionIds" item="institutionId" index="index" separator="," close=")" open="(">
            #{institutionId}
        </foreach>
        GROUP BY mic.id
    </select>

</mapper>
package com.get.salecenter.feign;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.financecenter.dto.BatchDownloadAgentReconciliationDto;
import com.get.financecenter.dto.query.AgentSettlementQueryDto;
import com.get.financecenter.vo.AgentSettlementGrossAmountVo;
import com.get.institutioncenter.dto.NewEmailGetAgentDto;
import com.get.pmpcenter.dto.agent.AgentCommissionTypeAgentDto;
import com.get.rocketmqcenter.dto.InsurancePlanMessageDto;
import com.get.salecenter.dto.CommissionSummaryDto;
import com.get.salecenter.dto.EventBillAccountNoticeDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.PayablePlanDto;
import com.get.salecenter.dto.StaffBdCodeDto;
import com.get.salecenter.dto.UpdatePayablePlanStatusSettlementDto;
import com.get.salecenter.entity.*;
import com.get.salecenter.vo.AgenCommissionAndAgentSearchVo;
import com.get.salecenter.vo.AgentContractAccountVo;
import com.get.salecenter.vo.AgentContractVo;
import com.get.salecenter.vo.AgentSettlementPageVo;
import com.get.salecenter.vo.ClientVo;
import com.get.salecenter.vo.CommissionSummaryPageVo;
import com.get.salecenter.vo.ConventionRegistrationVo;
import com.get.salecenter.vo.EventBillVo;
import com.get.salecenter.vo.EventCostVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.PayablePlanVo;
import com.get.salecenter.vo.ReceivablePlanVo;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.vo.StaffBdCodeVo;
import com.get.salecenter.vo.StudentOfferItemSendEmailVo;
import com.get.salecenter.vo.StudentOfferItemVo;
import com.get.salecenter.vo.StudentOfferVo;
import com.get.salecenter.vo.StudentPlanVo;
import com.get.salecenter.vo.StudentServiceFeeSummaryVo;
import com.get.salecenter.vo.StudentServiceFeeVo;
import com.google.common.collect.Maps;
import feign.Request;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.stereotype.Component;

/**
 * Feign失败配置
 */
@Component
@VerifyPermission(IsVerify = false)
public class ISaleCenterClientFallBack implements ISaleCenterClient {
    @Override
    public Result<List<StudentOfferItem>> getStudentOfferItemByParentId(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> analyzeOffer() {
        return Result.fail("操作失败");
    }
    @Override
    public Result<Map<Long, String>> getAgentNamesByIds(Set<Long> agentIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<PayablePlanVo>> getPayablePlan(String typeKey, Long targetId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getPayablePlanId(String typeKey, Long targetId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<PayablePlanVo> getPayablePlanDetail(Long payPlanId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<BigDecimal> getPayablePlanAmountById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getReceivablePlanId(String typeKey, Long targetId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<BigDecimal> getReceivablePlanAmountById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<ReceivablePlanVo> getReceivablePlanById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getNamesByStudentAppCountryIds(Set<Long> studentAppCountryIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getCurrencyNum(Long id) {
        return null;
    }

    @Override
    public Result<List<Long>> getStudentIds(String name) {
        return Result.fail("获取数据失败");
    }

    @Override
    public List<Long> getServiceFeeStudentIds(String targetName) {
        return null;
    }

    @Override
    public Result<List<Long>> getChannelIds(String tableName, String channelName) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getChannelNamesByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getStudentNameById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<StudentOfferVo> getStudentOfferDetail(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<StudentOfferItem> getStudentOfferItemById(Long id) {
        return null;
    }

    @Override
    public Result<List<SelItem>> getStudentOfferItemByIds(Set<Long> targetIds) {
        return null;
    }

    @Override
    public Result<Map<Long, String>> getStudentNameByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public List<BaseSelectEntity> getInvoiceStudentSelection(Long companyId) {
        return null;
    }

    @Override
    public Result<List<BaseSelectEntity>> getTargetName(Long companyId, String tableName) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> updateStudentOffer(StudentOfferVo studentOffer) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> nettyPush(Long itemId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> updateChangeStatus(AgentContract agentContract) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> startContractFlow(String businessKey, String procdefKey, String companyId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> changeStatus(Integer status, String tableName, Long businessKey) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<AgentContractVo> getAgentContractById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<String, String>> getNamesByMobiles(Set<String> mobiles) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Set<Long>> getAreaRegionIdsByCompanyId() {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<BaseSelectEntity>> getOfferItemSelectByAgentId(String tableName, Long agentId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<BaseSelectEntity>> getOfferItemSelectByProviderId(String tableName, Long providerId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getAgentIds(String name) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<BaseSelectEntity>> getAgentSelect(Long fkStaffId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getAgentNameById(Long agentId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Long> getStaffByAgentId(Long fkAgentId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public AgentSettlementPageVo agentSettlementList(SearchBean<AgentSettlementQueryDto> page) {
        return null;
    }

    @Override
    public List<Long> getAgentSettlementIds(AgentSettlementQueryDto agentSettlementVo, String local, Long staffId, boolean payInAdvanceFlag, boolean exportFlag) {
        return null;
    }

    @Override
    public boolean checkAgentData(List<BatchDownloadAgentReconciliationDto> batchDownloadAgentReconciliationVoList) {
        return false;
    }

    @Override
    public Integer getAgentCompanyIdById(Long id) {
        return null;
    }

    @Override
    public Map<Long,Long> getAgentCompanyIdByIds(Set<Long> ids) {
        return null;
    }

    @Override
    public Result<List<Long>> getSubordinateNotPortAgentIdsById(Long agentId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<StudentPlanVo> financePlanDetails(Long planId) {
        return Result.fail("获取数据失败");
    }

//    @Override
//    public Result<Boolean> financeConfirmSettlement(List<Long> payablePlanIdList) {
//        return Result.fail("操作失败");
//    }

    @Override
    public Result<Boolean> batchUpdatePayablePlan(List<PayablePlanDto> payablePlanDtoList) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<CommissionSummaryPageVo> commissionSummary(SearchBean<CommissionSummaryDto> page) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<PayablePlanVo>> getAgentPayablePlanByNumSettlementBatch(String numSettlementBatch) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, AgentContractAccountVo>> getAgentContractAccountByAccountIds(List<Long> accountIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, List<AgentContractAccountVo>>> getAgentContractAccountByAgentIds(List<Long> agentIds) {
        return null;
    }


    @Override
    public Result<String> getAgentContractBankAccountNameById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, List<EventCostVo>>> getEventCostDtoByReceiptFormIds(Set<Long> receiptFormIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<EventCostVo>> getEventCostDtoByReceiptFormId(Long receiptFormId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<ReceivablePlanVo>> getReceivablePlanDtosByIds(List<Long> fkReceivablePlanIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<ReceivablePlanVo>> getReceivablePlansDetail(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> deleteValidateCourse(Long courseId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<List<Long>> getAgentIdListByLoginStaffPower() {
        return Result.fail("获取数据失败");
    }


    @Override
    public Result<List<PayablePlanVo>> getPayablePlanByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<PayablePlanVo>> getPayablePlanDetailsByIds(Set<Long> ids) {
        return null;
    }

    @Override
    public Result<Map<Long, PayablePlanVo>> findOfferItemByPayIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, ReceivablePlanVo>> findOfferItemByReceivableIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, Object>> getDeferEntranceTimeByIds(Set<Long> ids) {
        return null;
    }

    @Override
    public Result<List<ReceivablePlanVo>> findReceivablePlanByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<BaseSelectEntity>> getPlanIdsByTableNameAndChannelId(String tableName, Long channelId,Long receiptFormId,String fkTypeKey,Integer pageNumber,Integer pageSize) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> agentIsKeyExpired() {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<AgentContractAccount> getAccount(Long agentId, String backNum) {
        return null;
    }

    @Override
    public Result<List<StudentOfferItemVo>> getStudentOfferItemByStudentOfferItemStepId(List<Long> fkTableIds) {
        return Result.fail("获取数据失败");
    }


    @Override
    public Result<PayablePlan> getPayablePlanByReceivablePlanId(Long fkReceivablePlanId) {
        return Result.fail("取消失败");
    }

    @Override
    public Result<List<PayablePlan>> getPayablePlanByReceivablePlanIds(List<Long> fkReceivablePlanIdList) {
        return null;
    }

    @Override
    public Result<List<BaseSelectEntity>> getOfferItemSelectByChannelId(String tableName, Long channelId, Long receiptFormId,Integer pageNumber,Integer pageSize) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<ReceivablePlanVo>> getReceivablePlansDetailNew(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public void dataImport() {

    }

    @Override
    public Result<List<BaseSelectEntity>> getOfferItemSelectByProviderIdNew(String tableName, Long providerId, Long receiptFormId,Integer pageNumber,Integer pageSize) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Page> getReceiptFormReceivablePlanPaginationInfo(String tableName, Long channelId, Long receiptFormId, String fkTypeKey,Page page) {
        return null;
    }

    @Override
    public Result<Map<String,Set<Long>>> getInChannelAndProviderIds(Set<Long> targetIds) {
        return null;
    }

    @Override
    public Result<List<Long>> getStudentOfferItemByPayIds(Set<Long> ids) {
        return null;
    }

    @Override
    public Result<List<MediaAndAttachedVo>> getItemMedias(Set<Long> ids) {
        return null;
    }

    @Override
    public Result<List<MediaAndAttachedVo>> getAgentCommissionMedias(Set<Long> ids) {
        return null;
    }

    @Override
    public Result<List<ReceivablePlanVo>> packReceivablePlanResult(List<ReceivablePlanVo> receivablePlanVos) {
        return null;
    }

    @Override
    public Result<PayablePlan> getPayableInfoById(Long payablePlanId) {
        return null;
    }

    @Override
    public Result<Long> getAgentIdByPayablePlanId(Long payablePlanId) {
        return null;
    }

    @Override
    public Result<List<BaseSelectEntity>> getPlanIdsAndCompanyName(String typeKey, Long targetId,Long receiptFormId) {
        return null;
    }

    @Override
    public Result<Long> getAccommodationAgentId(Long targetId) {
        return null;
    }

    @Override
    public Result<Long> getInsuranceAgentId(Long targetId) {
        return null;
    }

    @Override
    public Result<Long> getStudentAccommodationId(Long targetId) {
        return null;
    }

    @Override
    public Result<StudentAccommodation> getStudentAccommodationById(Long id) {
        return null;
    }

    @Override
    public Result<Long> getStudentInsuranceId(Long targetId) {
        return null;
    }

    @Override
    public Result<StudentInsurance> getStudentInsuranceById(Long id) {
        return null;
    }

    @Override
    public Result<Map<Long, Agent>> getAgentsByIds(Set<Long> agentIds) {
        return null;
    }

    @Override
    public Result<Set<Long>> getBusinessId(Map<String, Set<Long>> params) {
        return null;
    }


    @Override
    public Result<Map<Long, String>> getAStudyAccommodationProvider(Set<Long> ids) {
        return null;
    }

    @Override
    public Result<List<Long>> getBusinessProviderId(String targetName) {
        return null;
    }

    @Override
    public String getBusinessProviderNameById(Long id) {
        return null;
    }

    @Override
    public Result<Set<Long>> getBusinessProviderIdByAccIds(Set<Long> ids) {
        return null;
    }

    @Override
    public Result<List<BaseSelectEntity>> getBusinessObjectSelection(Long companyId) {
        return null;
    }

    @Override
    public Result<List<BaseSelectEntity>> getPlanAndTargetName(Long targetId, Long receiptFormId) {
        return null;
    }

    @Override
    public Result<List<String>> checkAgentContractByAgentIds(Set<Long> agentIdSet) {
        return null;
    }

    @Override
    public Result<List<AgentContractVo>> getAgentContractsByEndTime(String date) {
        return Result.fail("获取数据失败");
    }

    @Override
    public List<SaleMediaAndAttached> getMediaAndAttachedByIaeCrm() {
        return null;
    }

    @Override
    public Boolean updateMediaAndAttachedById(SaleMediaAndAttached mediaAndAttached) {
        return null;
    }

    @Override
    public Result<Boolean> autoRelationReceipten(Set<Long> receivablePlanIds) {
        return null;
    }

    @Override
    public Result<List<BaseSelectEntity>> getItemStepSelect() {
        return null;
    }

    @Override
    public Result<Set<String>> getItemStepSelectByStepKey(List<String> stepKeys) {
        return null;
    }

    @Override
    public Result<StudentOfferVo> getStudentOfferForWorkFlow(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<ResponseBo> sendEventBillAccountEmail(EventBillAccountNoticeDto eventBillAccountNoticeDto) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<BaseSelectEntity>> getStudentServiceFeeReceivablePlan(Long targetId, Long receiptFormId, Integer pageNumber, Integer pageSize) {
        return null;
    }

    @Override
    public List<Long> getServiceFeeStudentIdsByIds(List<Long> ids) {
        return Collections.emptyList();
    }

    @Override
    public Result<Long> getServiceFeeStudentIdsById(Long targetId) {
        return null;
    }

    @Override
    public Result<StudentServiceFeeVo> getServiceFeeById(Long targetId) {
        return null;
    }

    @Override
    public Result<Map<Long, String>> getBusinessProviderNameByIds(Set<Long> businessProviderIds) {
        return null;
    }

    @Override
    public List<BaseSelectEntity> getAgentByTargetName(String targetName) {
        return null;
    }

    @Override
    public List<BaseSelectEntity> getBusinessProviderByTargetName(String targetName) {
        return null;
    }


    @Override
    public Result<Map<Long, Boolean>> getCommissionActiveStatusByInstitutionIds(Set<Long> institutionIds) {
        return Result.data(Maps.newHashMap());
    }

    @Override
    public List<ReceivablePlanVo> getReceivableAmountInfo(Set<Long> receivablePlanIds) {
        return null;
    }

    @Override
    public Map<Long, List<AgentContract>> getAgentContractByAgentIds(List<Long> agentIds) {
        return null;
    }

    @Override
    public Result<String> getContractBankAccountNameById(Long fkBankAccountId, String fkTypeKey) {
        return null;
    }



    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedByAgentIds(List<String> fkAgentIds_) {
        return null;
    }

    @Override
    public Result<Map<Long, List<Long>>> getBdIdByAgentIds(Set<Long> agentIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> getProviderInstitutionItem(Long fkInstitutionId,Long fkInstitutionProviderId) {
        return null;
    }

    @Override
    public List<ReceivablePlanVo> getReceivablePlansBySort(Set<Long> planIds, Boolean invoiceAmountSort, Boolean receiptAmountSort, String studentName, Long receiptFormId) {
        return null;
    }

    @Override
    public List<StudentOfferItemVo> getStudentByOfferItemIds(List<Long> itemIds) {
        return null;
    }

    @Override
    public Map<String, String> getContactPersonEmailMap(Long itemId) {
        return null;
    }

    @Override
    public StudentOfferItemSendEmailVo getContactPersonEmailStaff(Long itemId) {
        return null;
    }

    @Override
    public void dataImportItemEmail() {

    }


    @Override
    public Map<Long, Object> getAgentContractPersonMobileByAgentId(Set<Long> agentIds) {
        return null;
    }

    @Override
    public Result<Map<Long,NameLabel>> getNameLabelListByFkTableName(String fkTableName){return null;}

    @Override
    public Result<List<AgentSettlementGrossAmountVo>> getAgentListToExport(AgentSettlementQueryDto agentSettlementVo) {
        return null;
    }

    @Override
    public Result<Set<String>> getNewAgentEmails(NewEmailGetAgentDto newEmailGetAgentDto, Request.Options options) {
        return null;
    }

    @Override
    public Result<Set<String>> getNewAgentAllEmails(Long newsId, Integer type, Request.Options options) {
        return null;
    }

    @Override
    public Result<List<Long>> getServiceFeeProviderIdsByFeeIds(List<Long> feeIds) {
        return null;
    }

    @Override
    public Result<List<BaseSelectEntity>> getReceivablePlanSelectByProvider(Long providerId, Long receiptFormId) {
        return null;
    }

    @Override
    public void kpiPlanStatistics() {
    }

    @Override
    public Result<List<BaseSelectEntity>> getBusinessProviderSelectByTypeKey(Long companyId, String typeKey) {
        return null;
    }
    @Override
    public Result<AgenCommissionAndAgentSearchVo> getAgentCommissionTypeAndAgentIsBind(SearchBean<AgentCommissionTypeAgentDto> page) {
        return null;
    }

    @Override
    public Result updatePayablePlanStatusSettlement(UpdatePayablePlanStatusSettlementDto updatePayablePlanStatusSettlementDto) {
        return null;
    }

    @Override
    public Result<Boolean> saveBatchMediaAndAttached(List<MediaAndAttachedDto> mediaAndAttachedDtos) {
        return null;
    }

    @Override
    public Result<Map<Long, List<MediaAndAttachedVo>>> getMediaAndAttachedDtoByFkTableIds(String key, Set<Long> fkTableId) {
        return null;
    }

    @Override
    public Result<Agent> getAgentById(Long fkAgentId) {
        return null;
    }

    @Override
    public Result<Student> getStudentById(Long studentId) {
        return null;
    }

    @Override
    public Result<Set<Long>> getRoleAndStaffByTableIdAndRoles(Long fkStudentOfferId, String key, List<String> roleList) {
        return null;
    }

    @Override
    public Result<EventBillVo> getEventBillById(Long eventBillId) {
        return null;
    }

    @Override
    public Result createInsurancePlan(InsurancePlanMessageDto insurancePlanMessageDto) {
        return null;
    }

    @Override
    public Result<List<BaseSelectEntity>> getInsuranceOrderSelect(Long companyId) {
        return null;
    }

    @Override
    public Result<List<BaseSelectEntity>> getPlanIdsByBusinessProviderId(Long targetId, Long receiptFormId) {
        return null;
    }

    @Override
    public Result<List<ConventionRegistrationVo>> getConventionRegistrationByReceiptCode(List<String> receiptCodeList) {
        return null;
    }

    @Override
    public Result<Convention> getConventionRegistrationById(Long id) {
        return null;
    }

    @Override
    public Result<Map<Long, String>> getInsuranceProviderNameByIds(Set<Long> ids) {
        return null;
    }

    @Override
    public List<StudentProjectRole> getStudentProjectRoleListByRoleIds(Set<Long> roleIds) {
        return null;
    }

    @Override
    public Result<StudentServiceFeeSummaryVo> getServiceFeeInfoById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<StudentServiceFeeVo> findServiceFeeById(Long fkTableId) {
        return null;
    }

    @Override
    public Result<List<StaffBdCodeVo>> getBdInformation(SearchBean<StaffBdCodeDto> page) {
        return null;
    }

    @Override
    public Result<List<MediaAndAttachedVo>> addMediaAndAttachedList(List<MediaAndAttachedDto> mediaAttachedVos) {
        return null;
    }

    @Override
    public Result<Boolean> copyPartnerStudentAttached(Long studentId, Long partnerStudentId) {
        return null;
    }

    @Override
    public Result<ClientVo> findClientById(Long fkTableId) {
        return null;
    }

    @Override
    public Result<Boolean> sendAgentContractUnsignedReminders() {
        return Result.fail("发送代理合同未签署提醒邮件失败");
    }

}
